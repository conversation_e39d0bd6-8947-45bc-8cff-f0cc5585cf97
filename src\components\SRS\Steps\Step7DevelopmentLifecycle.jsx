import { useState, useRef } from "react";
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  Typography,
  Button,
  Space,
  Divider,
} from "antd";
import { Clock, Plus } from "lucide-react";
import { motion } from "framer-motion";
import {
  DEVELOPMENT_APPROACHES,
  COLLABORATION_TOOLS,
} from "../../../constants/srsFormData";

const { Title } = Typography;
const { TextArea } = Input;

const Step7DevelopmentLifecycle = ({ form, setAllFormData }) => {
  const [developmentOptions, setDevelopmentOptions] = useState([
    ...DEVELOPMENT_APPROACHES,
  ]);
  const [newApproach, setNewApproach] = useState("");
  const [newIntegration, setNewIntegration] = useState("");
  const approachInputRef = useRef(null);

  const [toolOptions, setToolOptions] = useState({
    projectManagement: [...COLLABORATION_TOOLS.projectManagement],
    versionControl: [...COLLABORATION_TOOLS.versionControl],
    communication: [...COLLABORATION_TOOLS.communication],
    documentation: [...COLLABORATION_TOOLS.documentation],
    design: [...COLLABORATION_TOOLS.design],
  });

  const [newTool, setNewTool] = useState({
    projectManagement: "",
    versionControl: "",
    communication: "",
    documentation: "",
    design: "",
  });

  const inputRefs = useRef(null);
  const [integrationOptions, setIntegrationOptions] = useState([]);

  const toolInputRefs = {
    projectManagement: useRef(null),
    versionControl: useRef(null),
    communication: useRef(null),
    documentation: useRef(null),
    design: useRef(null),
  };

  // Add custom development approach
  const addCustomApproach = (e) => {
    e.preventDefault();
    if (
      newApproach.trim() &&
      !developmentOptions.includes(newApproach.trim())
    ) {
      setDevelopmentOptions((prev) => [...prev, newApproach.trim()]);
      setNewApproach("");
      setTimeout(() => {
        approachInputRef.current?.focus();
      }, 0);
    }
  };

  // Add custom tool to a category
  const addCustomTool = (category, e) => {
    e.preventDefault();
    const value = newTool[category]?.trim();
    if (value && !toolOptions[category].includes(value)) {
      setToolOptions((prev) => ({
        ...prev,
        [category]: [...prev[category], value],
      }));
      setNewTool((prev) => ({ ...prev, [category]: "" }));
      setTimeout(() => {
        toolInputRefs[category].current?.focus();
      }, 0);
    }
  };
  console.log(integrationOptions);
  const addCustomIntegration = (e) => {
    e.preventDefault();
    const value = newIntegration?.trim();
    console.log(value);
    if (value && !integrationOptions.includes(value)) {
      setIntegrationOptions((prev) => [...prev, value]);
      setNewIntegration("");
      setTimeout(() => {
        inputRefs.current?.focus();
      }, 0);
    }
  };

  const onChangeFormStore = (key, value) => {
    setAllFormData((prev) => ({
      ...prev,
      ["5"]: {
        ...prev["5"],
        [key]: value,
      },
    }));
  };

  const toolCategories = [
    {
      key: "projectManagement",
      label: "Project Management",
      icon: "📋",
      description: "Task tracking and project organization tools",
    },
    {
      key: "versionControl",
      label: "Version Control",
      icon: "🔄",
      description: "Code repository and version management",
    },
    {
      key: "communication",
      label: "Team Communication",
      icon: "💬",
      description: "Team chat and collaboration platforms",
    },
    {
      key: "documentation",
      label: "Documentation",
      icon: "📚",
      description: "Documentation and knowledge management",
    },
    {
      key: "design",
      label: "Design & Prototyping",
      icon: "🎨",
      description: "UI/UX design and prototyping tools",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <Clock className="mr-3 text-blue-600" size={24} />
          Development Lifecycle & Methodology
        </Title>

        {/* Development Approach */}
        <Row gutter={[24, 24]} className="mb-6">
          <Col xs={24} md={24} key={"third-party"} className={"mb-4"}>
            <Form.Item
              label="Third-Party Integrations"
              name={`thirdPartyList`}
              className="mb-0"
            >
              <Select
                mode="multiple"
                placeholder={`Select Third-Party Integrations`}
                size="large"
                className="w-full"
                value={form.getFieldValue(`thirdPartyList`)}
                onChange={(value) => {
                  form.setFieldValue(`thirdPartyList`, value);
                  onChangeFormStore(`thirdPartyList`, value);
                  // setAllFormData((prev) => ({
                  //   ...prev,
                  //   thirdPartyList: value,
                  // }));
                  console.log(`📝 Step6: integrations_ updated:`, value);
                }}
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
                popupRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: "8px 0" }} />
                    <Space style={{ padding: "0 8px 4px" }}>
                      <Input
                        placeholder={`Add custom integration`}
                        ref={inputRefs}
                        value={newIntegration}
                        onChange={(e) => setNewIntegration(e.target.value)}
                        onKeyDown={(e) => {
                          e.stopPropagation();
                          if (e.key === "Enter") {
                            addCustomIntegration(e);
                          }
                        }}
                        style={{ width: 160 }}
                      />
                      <Button
                        type="text"
                        icon={<Plus size={14} />}
                        onClick={(e) => addCustomIntegration(e)}
                        disabled={!newIntegration?.trim()}
                      >
                        Add
                      </Button>
                    </Space>
                  </>
                )}
                options={integrationOptions?.map((option) => ({
                  label: option,
                  value: option,
                }))}
              />
            </Form.Item>
          </Col>

          <Col xs={24}>
            <Form.Item
              name="developmentApproach"
              label="Development Methodology"
              rules={[
                {
                  required: true,
                  message: "Please select development approach",
                },
              ]}
            >
              <Select
                mode="multiple"
                placeholder="Select development methodologies"
                size="large"
                className="w-full"
                value={form.getFieldValue("developmentApproach")}
                onChange={(value) => {
                  form.setFieldValue("developmentApproach", value);
                  onChangeFormStore("developmentApproach", value);
                  // setAllFormData((prev) => ({
                  //   ...prev,
                  //   developmentApproach: value,
                  // }));
                  console.log("📝 Step7: developmentApproach updated:", value);
                }}
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
                popupRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: "8px 0" }} />
                    <Space style={{ padding: "0 8px 4px" }}>
                      <Input
                        placeholder="Add custom methodology"
                        ref={approachInputRef}
                        value={newApproach}
                        onChange={(e) => setNewApproach(e.target.value)}
                        onKeyDown={(e) => {
                          e.stopPropagation();
                          if (e.key === "Enter") {
                            addCustomApproach(e);
                          }
                        }}
                        style={{ width: 180 }}
                      />
                      <Button
                        type="text"
                        icon={<Plus size={14} />}
                        onClick={addCustomApproach}
                        disabled={!newApproach.trim()}
                      >
                        Add
                      </Button>
                    </Space>
                  </>
                )}
                options={developmentOptions.map((approach) => ({
                  label: approach,
                  value: approach,
                }))}
              />
            </Form.Item>
          </Col>

          <Col xs={24}>
            <Form.Item name="developmentPhases" label="Project Phases">
              <Select
                mode="multiple"
                placeholder="Select project phases"
                size="large"
                className="w-full"
                value={form.getFieldValue("developmentPhases")}
                onChange={(value) => {
                  form.setFieldValue("developmentPhases", value);
                  onChangeFormStore("developmentPhases", value);
                }}
                options={[
                  { label: "Discovery & Planning", value: "discovery" },
                  { label: "Design & Prototyping", value: "design" },
                  {
                    label: "Development & Implementation",
                    value: "development",
                  },
                  { label: "Testing & Quality Assurance", value: "testing" },
                  { label: "Deployment & Launch", value: "deployment" },
                  { label: "Maintenance & Support", value: "maintenance" },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        {/* Collaboration Tools */}
        <Title level={4} className="mb-4">
          Collaboration Tools & Platforms
        </Title>

        <Row gutter={[24, 24]}>
          {toolCategories?.map(({ key, label, icon, description }) => (
            <Col xs={24} md={12} key={key}>
              <div className="border border-gray-200 rounded-lg p-4 h-full">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-xl">{icon}</span>
                  <div>
                    <Title level={5} className="mb-0">
                      {label}
                    </Title>
                    <p className="text-xs text-gray-500 mb-0">{description}</p>
                  </div>
                </div>

                <Form.Item name={`tools_${key}`} className="mb-0">
                  <Select
                    mode="multiple"
                    placeholder={`Select ${label.toLowerCase()}`}
                    size="large"
                    className="w-full"
                    value={form.getFieldValue(`tools_${key}`)}
                    onChange={(value) => {
                      form.setFieldValue(`tools_${key}`, value);
                      onChangeFormStore(`tools_${key}`, value);
                      // setAllFormData((prev) => ({
                      //   ...prev,
                      //   [`tools_${key}`]: value,
                      // }));
                      console.log(`📝 Step7: tools_${key} updated:`, value);
                    }}
                    filterOption={(input, option) =>
                      option.label.toLowerCase().includes(input.toLowerCase())
                    }
                    popupRender={(menu) => (
                      <>
                        {menu}
                        <Divider style={{ margin: "8px 0" }} />
                        <Space style={{ padding: "0 8px 4px" }}>
                          <Input
                            placeholder={`Add custom ${label.toLowerCase()}`}
                            ref={toolInputRefs[key]}
                            value={newTool[key]}
                            onChange={(e) =>
                              setNewTool((prev) => ({
                                ...prev,
                                [key]: e.target.value,
                              }))
                            }
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === "Enter") {
                                addCustomTool(key, e);
                              }
                            }}
                            style={{ width: 160 }}
                          />
                          <Button
                            type="text"
                            icon={<Plus size={14} />}
                            onClick={(e) => addCustomTool(key, e)}
                            disabled={!newTool[key]?.trim()}
                          >
                            Add
                          </Button>
                        </Space>
                      </>
                    )}
                    options={toolOptions[key].map((tool) => ({
                      label: tool,
                      value: tool,
                    }))}
                  />
                </Form.Item>
              </div>
            </Col>
          ))}
        </Row>

        <Divider />

        {/* Additional Development Details */}
        <Row gutter={[24, 24]}>
          <Col xs={24}>
            <Form.Item name="customTools" label="Additional Tools & Processes">
              <TextArea
                rows={3}
                placeholder="Describe any custom tools, processes, or specific requirements not listed above..."
                className="rounded-lg"
                value={form.getFieldValue("customTools")}
                onChange={(e) => {
                  form.setFieldValue("customTools", e.target.value);
                  onChangeFormStore("customTools", e.target.value);
                  // setAllFormData((prev) => ({
                  //   ...prev,
                  //   customTools: e.target.value,
                  // }));
                  console.log("📝 Step7: customTools updated:", e.target.value);
                }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item name="testingStrategy" label="Testing Strategy">
              <Select
                mode="multiple"
                placeholder="Select testing approaches"
                size="large"
                value={form.getFieldValue("testingStrategy")}
                onChange={(value) => {
                  form.setFieldValue("testingStrategy", value);
                  onChangeFormStore("testingStrategy", value);
                  // setAllFormData((prev) => ({
                  //   ...prev,
                  //   testingStrategy: value,
                  // }));
                  console.log("📝 Step7: testingStrategy updated:", value);
                }}
                options={[
                  { label: "Unit Testing", value: "unit" },
                  { label: "Integration Testing", value: "integration" },
                  { label: "End-to-End Testing", value: "e2e" },
                  { label: "Performance Testing", value: "performance" },
                  { label: "Security Testing", value: "security" },
                  { label: "User Acceptance Testing", value: "uat" },
                  { label: "Manual Testing", value: "manual" },
                  { label: "Automated Testing", value: "automated" },
                ]}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item name="deploymentStrategy" label="Deployment Strategy">
              <Select
                mode="multiple"
                placeholder="Select deployment approaches"
                size="large"
                value={form.getFieldValue("deploymentStrategy")}
                onChange={(value) => {
                  form.setFieldValue("deploymentStrategy", value);
                  onChangeFormStore("deploymentStrategy", value);
                  // setAllFormData((prev) => ({
                  //   ...prev,
                  //   deploymentStrategy: value,
                  // }));
                  console.log("📝 Step7: deploymentStrategy updated:", value);
                }}
                options={[
                  { label: "Continuous Integration (CI)", value: "ci" },
                  { label: "Continuous Deployment (CD)", value: "cd" },
                  { label: "Blue-Green Deployment", value: "blue-green" },
                  { label: "Rolling Deployment", value: "rolling" },
                  { label: "Canary Deployment", value: "canary" },
                  { label: "Manual Deployment", value: "manual" },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Development Guidelines */}
        <div className="mt-6 p-4 bg-indigo-50 rounded-lg border border-indigo-200">
          <Title level={5} className="text-indigo-800 mb-2">
            Development Methodology Guidelines
          </Title>
          <ul className="text-sm text-indigo-700 space-y-1">
            <li>
              • Choose methodologies that align with your team size and project
              complexity
            </li>
            <li>
              • Consider stakeholder involvement and feedback frequency
              requirements
            </li>
            <li>
              • Select tools that integrate well together and support your
              workflow
            </li>
            <li>• Plan for scalability as your team and project grow</li>
          </ul>
        </div>
      </Card>
    </motion.div>
  );
};

export default Step7DevelopmentLifecycle;
