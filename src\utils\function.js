

// Sanitize data to remove circular references and React elements (MOVED OUTSIDE COMPONENT)
export const sanitizeData = (data) => {
    try {
        return JSON.parse(
            JSON.stringify(data, (key, value) => {
                // Remove React elements and functions
                if (
                    typeof value === "function" ||
                    (value && value.$$typeof) ||
                    (value && value._owner)
                ) {
                    return undefined;
                }
                return value;
            })
        );
    } catch (error) {
        console.warn("Data sanitization failed:", error);
        return {};
    }
};

export const sanitizeDataNavigation = (data) => {
    if (data === null || data === undefined) {
        return data;
    }

    // Handle primitive types
    if (typeof data !== "object") {
        return data;
    }

    // Handle arrays
    if (Array.isArray(data)) {
        return data?.map((item) => sanitizeData(item));
    }

    // Handle objects
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
        // Skip React-specific properties and DOM elements
        if (
            key.startsWith("__react") ||
            key.startsWith("_react") ||
            key === "nativeEvent" ||
            key === "currentTarget" ||
            key === "target" ||
            (value &&
                typeof value === "object" &&
                value.constructor &&
                (value.constructor.name.includes("HTML") ||
                    value.constructor.name.includes("Element") ||
                    value.constructor.name.includes("Node")))
        ) {
            continue;
        }

        // Recursively sanitize nested objects
        try {
            sanitized[key] = sanitizeData(value);
        } catch {
            // Skip properties that cause circular references
            console.warn(`Skipping property ${key} due to circular reference`);
            continue;
        }
    }

    return sanitized;
};