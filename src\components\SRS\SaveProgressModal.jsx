import React from "react";
import { Modal, Button, Typography, Space, Progress, Tag } from "antd";
import {
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";

const { Title, Text } = Typography;

const SaveProgressModal = ({
  visible,
  onCancel,
  onSaveAndStartNew,
  onContinueCurrent,
  onDiscardAndStartNew,
  currentStep = 0,
  totalSteps = 8,
  projectName = "Untitled Project",
  loading = false,
}) => {
  const completionPercentage = Math.round((currentStep / totalSteps) * 100);

  return (
    <Modal
      title={
        <div style={{ textAlign: "center" }}>
          <Title level={3} style={{ margin: 0, color: "#fa8c16" }}>
            ⚠️ SRS Generation In Progress
          </Title>
          <Text type="secondary">
            You have an existing SRS generation that's not completed
          </Text>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={600}
      footer={null}
      destroyOnClose
    >
      <div style={{ padding: "20px 0" }}>
        {/* Current Progress Info */}
        {/* <div style={{ 
          marginBottom: 24,
          padding: 16,
          backgroundColor: '#fff7e6',
          borderRadius: 8,
          border: '1px solid #ffd591'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
            <div>
              <Text strong style={{ fontSize: '16px', color: '#fa8c16' }}>
                📋 Current Progress
              </Text>
              <br />
              <Text style={{ fontSize: '14px', color: '#666' }}>
                {projectName}
              </Text>
            </div>
            <Tag color="orange" style={{ fontSize: '12px' }}>
              Step {currentStep} of {totalSteps}
            </Tag>
          </div>
          
          <Progress
            percent={completionPercentage}
            strokeColor="#fa8c16"
            trailColor="#f0f0f0"
            showInfo={false}
            style={{ marginBottom: 8 }}
          />
          
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {completionPercentage}% completed
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {totalSteps - currentStep} steps remaining
            </Text>
          </div>
        </div> */}

        {/* Question */}
        <div style={{ textAlign: "center", marginBottom: 24 }}>
          <Title level={4} style={{ color: "#1890ff" }}>
            What would you like to do?
          </Title>
          <Text type="secondary">
            Choose how to handle your current progress before starting a new SRS
          </Text>
        </div>

        {/* Action Buttons */}
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          {/* Save & Start New */}
          <Button
            type="primary"
            size="large"
            icon={<SaveOutlined />}
            onClick={onSaveAndStartNew}
            loading={loading}
            style={{
              width: "100%",
              height: "auto",
              padding: "16px",
              backgroundColor: "#52c41a",
              borderColor: "#52c41a",
            }}
          >
            <div style={{ textAlign: "left" }}>
              <div style={{ fontSize: "16px", fontWeight: "bold" }}>
                💾 Save Progress & Start New SRS
              </div>
              <div style={{ fontSize: "12px", opacity: 0.9, marginTop: 4 }}>
                Save current progress to history and begin a fresh SRS
                generation
              </div>
            </div>
          </Button>

          {/* Continue Current */}
          <Button
            size="large"
            icon={<EditOutlined />}
            onClick={onContinueCurrent}
            disabled={loading}
            style={{
              width: "100%",
              height: "auto",
              padding: "16px",
              borderColor: "#1890ff",
              color: "#1890ff",
            }}
          >
            <div style={{ textAlign: "left" }}>
              <div style={{ fontSize: "16px", fontWeight: "bold" }}>
                ✏️ Continue Current SRS
              </div>
              <div style={{ fontSize: "12px", opacity: 0.7, marginTop: 4 }}>
                Return to your current SRS generation and continue from step{" "}
                {currentStep}
              </div>
            </div>
          </Button>

          {/* Discard & Start New */}
          {/* <Button
            danger
            size="large"
            icon={<DeleteOutlined />}
            onClick={onDiscardAndStartNew}
            disabled={loading}
            style={{ 
              width: '100%', 
              height: 'auto', 
              padding: '16px'
            }}
          >
            <div style={{ textAlign: 'left' }}>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                🗑️ Discard Progress & Start New
              </div>
              <div style={{ fontSize: '12px', opacity: 0.7, marginTop: 4 }}>
                Permanently delete current progress and start a completely fresh SRS
              </div>
            </div>
          </Button> */}
        </Space>

        {/* Warning Note */}
        {/* <div style={{ 
          marginTop: 20,
          padding: 12,
          backgroundColor: '#fff2f0',
          border: '1px solid #ffccc7',
          borderRadius: 6
        }}>
          <Text style={{ fontSize: '12px', color: '#ff4d4f' }}>
            ⚠️ <strong>Important:</strong> If you choose "Discard Progress", all your current form data and progress will be permanently lost and cannot be recovered.
          </Text>
        </div> */}

        {/* Tip */}
        <div
          style={{
            marginTop: 12,
            padding: 12,
            backgroundColor: "#f6ffed",
            border: "1px solid #d9f7be",
            borderRadius: 6,
          }}
        >
          <Text style={{ fontSize: "12px", color: "#52c41a" }}>
            💡 <strong>Tip:</strong> Saving progress allows you to resume your
            SRS generation later from the History page. You can have multiple
            saved drafts.
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default SaveProgressModal;
