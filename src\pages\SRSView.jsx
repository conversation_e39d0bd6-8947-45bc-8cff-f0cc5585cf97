import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Button,
  Breadcrumb,
  Card,
  Space,
  Typography,
  message,
  Spin,
  Result,
} from "antd";
import { ArrowLeft, Edit3, Home, FileText, History } from "lucide-react";
import { motion } from "framer-motion";
import SRSDocumentViewer from "../components/SRS/SRSDocumentViewer";
import { useSRSHistory } from "../hooks/useLocalStorage";

const { Title } = Typography;

const SRSView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { history: documents } = useSRSHistory();

  const [document, setDocument] = useState(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);
  const loadedIdRef = useRef(null); // Track which ID we've already loaded

  // Load SRS document by ID
  useEffect(() => {
    const loadDocument = () => {
      try {
        // Prevent unnecessary re-loads of the same document
        if (loadedIdRef.current === id && document) {
          return;
        }

        setLoading(true);

        // Ensure documents is available and is an array
        if (!documents || !Array.isArray(documents)) {
          console.log("Documents not yet loaded or invalid format");
          setLoading(false);
          return;
        }

        // Find document by ID in the documents array
        const foundDocument = documents.find((doc) => doc && doc.id === id);

        if (foundDocument) {
          // Check if it's a completed document (not in-progress)
          if (foundDocument.metadata?.status === "in-progress") {
            message.warning(
              "Cannot view in-progress SRS. Please complete generation first."
            );
            navigate("/history");
            return;
          }

          setDocument(foundDocument);
          setNotFound(false);
          loadedIdRef.current = id; // Mark this ID as loaded
          console.log(
            "✅ SRS document loaded successfully:",
            foundDocument.projectInfo?.name
          );
        } else {
          console.log("❌ SRS document not found with ID:", id);
          setNotFound(true);
          loadedIdRef.current = id; // Mark this ID as attempted
        }
      } catch (error) {
        console.error("❌ Error loading SRS document:", error);
        message.error("Failed to load SRS document");
        setNotFound(true);
        loadedIdRef.current = id; // Mark this ID as attempted
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadDocument();
    }
  }, [id, documents]); // Only include essential dependencies to prevent infinite loops

  // Handle document save (when editing in viewer)
  const handleSaveDocument = async (updatedDocument) => {
    try {
      // Update document in localStorage
      const historyData = JSON.parse(
        localStorage.getItem("srs_history") || "[]"
      );
      const updatedHistory = historyData.map((doc) =>
        doc.id === updatedDocument.id ? updatedDocument : doc
      );
      localStorage.setItem("srs_history", JSON.stringify(updatedHistory));

      // Update local state
      setDocument(updatedDocument);
      message.success("Document updated successfully!");
    } catch (error) {
      console.error("Error saving document:", error);
      message.error("Failed to save document");
    }
  };

  // Handle edit button click - navigate to Generate page with edit mode
  const handleEdit = () => {
    navigate(`/generate?edit=${id}`);
  };

  // Handle back to history
  const handleBackToHistory = () => {
    navigate("/history");
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  // Document not found
  if (notFound || !document) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto p-6">
          <Result
            status="404"
            title="SRS Document Not Found"
            subTitle="The requested SRS document could not be found."
            extra={
              <Button type="primary" onClick={handleBackToHistory}>
                Back to History
              </Button>
            }
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with Breadcrumbs and Actions */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 py-4">
          {/* Breadcrumbs */}
          <Breadcrumb className="mb-4">
            <Breadcrumb.Item>
              <Button
                type="link"
                icon={<Home size={16} />}
                onClick={() => navigate("/")}
                className="p-0 flex items-start"
              >
                Home
              </Button>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <Button
                type="link"
                icon={<History size={16} />}
                onClick={handleBackToHistory}
                className="p-0 flex items-start"
              >
                History
              </Button>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <FileText size={16} className="inline mr-1" />
              {document.projectInfo?.name || "SRS Document"}
            </Breadcrumb.Item>
          </Breadcrumb>

          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <div>
              <Title level={2} className="mb-0">
                {document.projectInfo?.name || "SRS Document"}
              </Title>
              <p className="text-gray-600 mt-1">
                Version {document.metadata?.version} • Generated on{" "}
                {new Date(document.metadata?.generatedAt).toLocaleDateString()}
              </p>
            </div>

            <Space>
              <Button
                icon={<ArrowLeft size={16} />}
                onClick={handleBackToHistory}
              >
                Back to History
              </Button>
              <Button
                type="primary"
                icon={<Edit3 size={16} />}
                onClick={handleEdit}
              >
                Edit SRS
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* SRS Document Content */}
      <div className="max-w-7xl mx-auto px-4 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="shadow-lg">
            <SRSDocumentViewer
              document={document}
              onSave={handleSaveDocument}
              onClose={null} // No close button in page mode
              editable={true}
              mode="page" // New prop to indicate page mode
            />
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default SRSView;
