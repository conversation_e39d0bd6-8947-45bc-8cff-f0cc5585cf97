import React, { useState, useEffect } from "react";
import {
  Card,
  Button,
  Empty,
  Table,
  Tag,
  Space,
  Input,
  Select,
  message,
  Popconfirm,
  Typography,
  Modal,
} from "antd";
import {
  FileText,
  Download,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Search,
  Plus,
} from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useSRSHistory } from "../hooks/useLocalStorage";
import SRSDocumentViewer from "../components/SRS/SRSDocumentViewer";
import ConversationToggle from "../components/SRS/ConversationToggle";
// REMOVED: CostTracker import - no longer needed
// import conversationManager from "../services/conversationManager";
// import aiService from "../services/aiService";

const { Title } = Typography;
const { Search: SearchInput } = Input;

const History = () => {
  const navigate = useNavigate();
  const { history, removeFromHistory, updateInHistory, clearHistory } =
    useSRSHistory();

  const [selectedDocument, setSelectedDocument] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  // const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  // console.log("calling........");
  // Editing system state
  // const [editingDocument, setEditingDocument] = useState(null);
  // const [useOldConversation, setUseOldConversation] = useState(true);
  // const [isEditing, setIsEditing] = useState(false);
  // const [editRequest, setEditRequest] = useState("");

  // COMPLETELY REWRITTEN - NO useMemo to prevent infinite loops
  const [filteredDocuments, setFilteredDocuments] = useState([]);

  // Update filtered documents only when necessary
  useEffect(() => {
    if (!Array.isArray(history)) {
      setFilteredDocuments([]);
      return;
    }

    let result = [...history];

    // Apply search filter
    if (searchQuery && searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter((item) => {
        if (!item?.projectInfo) return false;
        const name = (item.projectInfo.name || "").toLowerCase();
        const description = (item.projectInfo.description || "").toLowerCase();
        return name.includes(query) || description.includes(query);
      });
    }

    // Apply status filter
    // if (statusFilter !== "all") {
    //   result = result.filter((doc) => {
    //     const status = doc?.metadata?.status || "completed";
    //     return status === statusFilter;
    //   });
    // }

    // Apply sorting
    result.sort((a, b) => {
      switch (sortBy) {
        case "newest": {
          const dateA = a?.metadata?.generatedAt
            ? new Date(a.metadata.generatedAt).getTime()
            : 0;
          const dateB = b?.metadata?.generatedAt
            ? new Date(b.metadata.generatedAt).getTime()
            : 0;
          return dateB - dateA;
        }
        case "oldest": {
          const dateA = a?.metadata?.generatedAt
            ? new Date(a.metadata.generatedAt).getTime()
            : 0;
          const dateB = b?.metadata?.generatedAt
            ? new Date(b.metadata.generatedAt).getTime()
            : 0;
          return dateA - dateB;
        }
        case "name": {
          const nameA = a?.projectInfo?.name || "Untitled";
          const nameB = b?.projectInfo?.name || "Untitled";
          return nameA.localeCompare(nameB);
        }
        default:
          return 0;
      }
    });

    setFilteredDocuments(result);
  }, [
    history,
    searchQuery,
    // statusFilter,
    sortBy,
  ]);

  // Handle document actions
  const handleView = (document) => {
    // Navigate to dedicated SRS view page instead of modal
    navigate(`/srs/${document.id}`);
  };

  // Handle edit action - navigate to Generate page with edit mode
  const handleEdit = (document) => {
    if (document.metadata?.status === "in-progress") {
      // For in-progress SRS, resume generation
      navigate(`/generate?resume=${document.id}`);
    } else {
      // For completed SRS, edit mode
      navigate(`/generate?edit=${document.id}`);
    }
  };

  // const handleEdit = (document) => {
  //   // FIXED: Check if this is an in-progress SRS
  //   if (document.metadata?.status === "in-progress") {
  //     // Navigate to generate page and load the progress
  //     handleResumeProgress(document);
  //   } else {
  //     // FIXED: Regular editing for completed SRS - show edit modal with question
  //     setEditingDocument(document);
  //     setSelectedDocument(null); // Close viewer if open
  //     setEditRequest(""); // Clear previous edit request
  //   }
  // };

  // Handle resuming in-progress SRS (fixed)
  // const handleResumeProgress = (inProgressDocument) => {
  //   try {
  //     console.log("🔄 Resuming progress for document:", inProgressDocument);

  //     // Validate input
  //     if (!inProgressDocument || !inProgressDocument.progressData) {
  //       throw new Error("Invalid in-progress document data");
  //     }

  //     const { progressData, formData } = inProgressDocument;

  //     // Create comprehensive resume data
  //     const resumeData = {
  //       // Include form data from both locations
  //       ...(formData || {}),
  //       ...(progressData.formData || {}),

  //       // Progress state
  //       currentStep: progressData.currentStep || 0,
  //       userRoles: progressData.userRoles || [],
  //       functionalModules: progressData.functionalModules || [],
  //       teamMembers: progressData.teamMembers || {},
  //       selectedPlatforms: progressData.selectedPlatforms || [],

  //       // Resume flags
  //       resumingFromHistory: true,
  //       originalId: inProgressDocument.id,
  //       resumedAt: new Date().toISOString(),
  //     };

  //     console.log("📥 Resume data prepared:", resumeData);

  //     // Store in localStorage for Generate page
  //     localStorage.setItem("srs_form_data", JSON.stringify(resumeData));

  //     console.log("✅ Resume data stored in localStorage");

  //     // FIXED: Navigate to generate page with proper message
  //     message.success(
  //       `Resuming SRS generation from step ${
  //         (progressData.currentStep || 0) + 1
  //       }. Redirecting to Generate page...`
  //     );

  //     // Small delay to show message before redirect
  //     setTimeout(() => {
  //       window.location.href = "/generate";
  //     }, 1000);
  //   } catch (error) {
  //     console.error("❌ Resume progress error:", error);
  //     message.error("Failed to resume progress. Please try again.");
  //   }
  // };

  // Handle conversation toggle change
  // const handleToggleChange = (useOld) => {
  //   setUseOldConversation(useOld);
  // };

  // Handle edit submission
  // const handleEditSubmit = async (editRequest) => {
  //   if (!editingDocument || !editRequest.trim()) {
  //     message.error("Please provide edit instructions");
  //     return;
  //   }

  //   try {
  //     setIsEditing(true);

  //     // Use AI service to edit with toggle preference
  //     const editedContent = await aiService.editSRS(
  //       editingDocument.id,
  //       editRequest,
  //       useOldConversation
  //     );

  //     // Update document in history
  //     const updatedDocument = {
  //       ...editingDocument,
  //       content: editedContent,
  //       metadata: {
  //         ...editingDocument.metadata,
  //         lastModified: new Date().toISOString(),
  //         editCount: (editingDocument.metadata.editCount || 0) + 1,
  //       },
  //     };

  //     updateInHistory(editingDocument.id, updatedDocument);

  //     message.success(
  //       `Document edited successfully using ${
  //         useOldConversation ? "conversation context" : "fresh approach"
  //       }!`
  //     );

  //     // Close editing modal and show updated document
  //     setEditingDocument(null);
  //     setEditRequest("");
  //     setSelectedDocument(updatedDocument);
  //   } catch (error) {
  //     console.error("Edit error:", error);
  //     message.error(`Failed to edit document: ${error.message}`);
  //   } finally {
  //     setIsEditing(false);
  //   }
  // };

  const handleDownload = (document, format = "md") => {
    const filename = `${document.projectInfo.name.replace(
      /\s+/g,
      "_"
    )}_SRS.${format}`;
    let content = document.content;
    let mimeType = "text/markdown";

    if (format === "txt") {
      content = content
        .replace(/#{1,6}\s/g, "")
        .replace(/\*\*(.*?)\*\*/g, "$1")
        .replace(/\*(.*?)\*/g, "$1");
      mimeType = "text/plain";
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    message.success(`Document downloaded as ${format.toUpperCase()}`);
  };

  const handleDelete = async (documentId) => {
    try {
      if (!documentId) {
        throw new Error("Document ID is required");
      }

      removeFromHistory(documentId);
      console.log("🗑️ Document deleted:", documentId);
      message.success("Document deleted successfully");
    } catch (error) {
      console.error("❌ Failed to delete document:", error);
      message.error("Failed to delete document");
    }
  };

  const handleSaveDocument = async (updatedDocument) => {
    try {
      if (!updatedDocument || !updatedDocument.id) {
        throw new Error("Invalid document data");
      }

      updateInHistory(updatedDocument.id, updatedDocument);
      console.log("💾 Document updated:", updatedDocument.id);
      message.success("Document updated successfully");
    } catch (error) {
      console.error("❌ Failed to update document:", error);
      message.error("Failed to update document");
    }
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Table columns
  const columns = [
    {
      title: "Document Name",
      dataIndex: ["projectInfo", "name"],
      key: "name",
      render: (text, record) => {
        const metadata = record?.metadata || {};
        const projectInfo = record?.projectInfo || {};

        return (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="text-blue-600" size={16} />
            </div>
            <div>
              <div className="font-medium text-gray-900">
                {projectInfo.name || text || "Untitled Project"}
              </div>
              <div className="text-sm text-gray-500">
                v{metadata.version || "1.0.0"} •{" "}
                {(metadata.wordCount || 0).toLocaleString()} words
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: "Platforms",
      dataIndex: ["formData", "selectedPlatforms"],
      key: "platforms",
      render: (platforms, record) => {
        const safePlatforms =
          platforms || record?.formData?.selectedPlatforms || [];

        if (!Array.isArray(safePlatforms) || safePlatforms.length === 0) {
          return (
            <Tag color="default" className="text-xs">
              No platforms
            </Tag>
          );
        }

        return (
          <Space wrap>
            {safePlatforms.slice(0, 2).map((platform, index) => (
              <Tag
                key={`${platform}-${index}`}
                color="blue"
                className="text-xs"
              >
                {platform || "Unknown"}
              </Tag>
            ))}
            {safePlatforms.length > 2 && (
              <Tag color="default" className="text-xs">
                +{safePlatforms.length - 2} more
              </Tag>
            )}
          </Space>
        );
      },
    },
    {
      title: "Status",
      dataIndex: ["metadata", "status"],
      key: "status",
      render: (status, record) => {
        const metadata = record?.metadata || {};
        const safeStatus = status || metadata.status || "completed";
        const isInProgress = safeStatus === "in-progress";

        return (
          <div>
            <Tag
              color={isInProgress ? "orange" : "green"}
              style={{ marginBottom: 4 }}
            >
              {isInProgress ? "🔄 In Progress" : "✅ Completed"}
            </Tag>
            {isInProgress && (
              <div style={{ fontSize: "11px", color: "#666" }}>
                Step {(metadata.currentStep || 0) + 1} of{" "}
                {metadata.totalSteps || 8}
                {" • "}
                {metadata.completionPercentage || 0}% complete
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: "AI Provider",
      dataIndex: ["metadata", "aiProvider"],
      key: "aiProvider",
      render: (provider, record) => {
        const safeProvider =
          provider || record?.metadata?.aiProvider || "unknown";
        return (
          <Tag
            color={
              safeProvider === "openai"
                ? "green"
                : safeProvider === "gemini"
                ? "purple"
                : "default"
            }
            className="capitalize"
          >
            {safeProvider === "unknown" ? "N/A" : safeProvider}
          </Tag>
        );
      },
    },
    {
      title: "Generated",
      dataIndex: ["metadata", "generatedAt"],
      key: "generatedAt",
      render: (date, record) => {
        const safeDate = date || record?.metadata?.generatedAt;
        return (
          <div className="flex items-center space-x-2">
            <Calendar size={14} className="text-gray-400" />
            <span className="text-gray-600">
              {safeDate ? formatDate(safeDate) : "Unknown"}
            </span>
          </div>
        );
      },
    },
    {
      title: "Read Time",
      dataIndex: ["metadata", "estimatedReadTime"],
      key: "readTime",
      render: (time, record) => {
        const safeTime = time || record?.metadata?.estimatedReadTime || 0;
        return <span className="text-gray-600">{safeTime} min</span>;
      },
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => {
        const metadata = record?.metadata || {};
        const isInProgress = metadata.status === "in-progress";
        const recordId = record?.id;

        if (!recordId) {
          return <span className="text-gray-400">No actions available</span>;
        }

        return (
          <Space>
            <Button
              type="text"
              icon={<Eye size={16} />}
              onClick={() => handleView(record)}
              className={
                isInProgress
                  ? "text-gray-400"
                  : "text-blue-600 hover:text-blue-700"
              }
              disabled={isInProgress}
              title={
                isInProgress
                  ? "Cannot view in-progress SRS"
                  : "View SRS document"
              }
            >
              View
            </Button>
            <Button
              type="text"
              icon={<Edit size={16} />}
              onClick={() => handleEdit(record)}
              className={
                isInProgress
                  ? "text-orange-600 hover:text-orange-700"
                  : "text-green-600 hover:text-green-700"
              }
              title={
                isInProgress ? "Resume SRS generation" : "Edit SRS document"
              }
            >
              {isInProgress ? "Resume" : "Edit"}
            </Button>
            <Button
              type="text"
              icon={<Download size={16} />}
              onClick={() => handleDownload(record)}
              className={
                isInProgress
                  ? "text-gray-400"
                  : "text-purple-600 hover:text-purple-700"
              }
              disabled={isInProgress}
              title={
                isInProgress
                  ? "Cannot download in-progress SRS"
                  : "Download SRS document"
              }
            >
              Download
            </Button>
            <Popconfirm
              title="Delete Document"
              description={`Are you sure you want to delete this ${
                isInProgress ? "in-progress" : "completed"
              } SRS document?`}
              onConfirm={() => handleDelete(recordId)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="text"
                icon={<Trash2 size={16} />}
                className="text-red-600 hover:text-red-700"
              >
                Delete
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-cyan-500 py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 text-center">
          <div className="text-white space-y-6">
            <h1 className="text-4xl lg:text-6xl font-bold">Document History</h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
              Manage and access all your generated SRS documents in one place.
            </p>
          </div>
        </div>
      </section>

      {/* History Content */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <div>
            {/* REMOVED: Cost Tracker - no longer needed */}

            <Card className="shadow-lg">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
                <div>
                  <Title level={2} className="mb-2">
                    Your SRS Documents ({filteredDocuments.length})
                  </Title>
                  <p className="text-gray-600">
                    View, edit, and download your generated documents.
                  </p>
                </div>

                <Link to="/generate">
                  <Button
                    type="primary"
                    size="large"
                    icon={<Plus size={16} />}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Create New SRS
                  </Button>
                </Link>
              </div>

              {/* Filters and Search */}
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <SearchInput
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  prefix={<Search size={16} />}
                  className="md:w-80"
                />

                <Select
                  value={sortBy}
                  onChange={setSortBy}
                  className="w-40"
                  options={[
                    { label: "Newest First", value: "newest" },
                    { label: "Oldest First", value: "oldest" },
                    { label: "Name A-Z", value: "name" },
                  ]}
                />

                {history.length > 0 && (
                  <Popconfirm
                    title="Clear All History"
                    description="Are you sure you want to delete all documents?"
                    onConfirm={clearHistory}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button danger icon={<Trash2 size={16} />}>
                      Clear All
                    </Button>
                  </Popconfirm>
                )}
              </div>

              {filteredDocuments.length > 0 ? (
                <Table
                  columns={columns}
                  dataSource={filteredDocuments}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `${range[0]}-${range[1]} of ${total} documents`,
                  }}
                  className="[&_.ant-table-thead>tr>th]:bg-gray-50 [&_.ant-table-thead>tr>th]:border-b-2 [&_.ant-table-thead>tr>th]:border-gray-200"
                />
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {searchQuery
                          ? "No documents found"
                          : "No SRS Documents Yet"}
                      </h3>
                      <p className="text-gray-600">
                        {searchQuery
                          ? "Try adjusting your search terms or filters."
                          : "You haven't created any SRS documents yet. Start by creating your first one!"}
                      </p>
                      {!searchQuery && (
                        <Link to="/generate">
                          <Button
                            type="primary"
                            size="large"
                            icon={<Plus size={16} />}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            Create Your First SRS
                          </Button>
                        </Link>
                      )}
                    </div>
                  }
                />
              )}
            </Card>
          </div>
        </div>
      </section>

      {/* Edit Document Modal with Conversation Toggle */}
      {/* {editingDocument && (
        <Modal
          title={
            <div style={{ textAlign: "center" }}>
              <Title level={3} style={{ margin: 0, color: "#1890ff" }}>
                ✏️ Edit SRS Document
              </Title>
              <p style={{ margin: 0, color: "#666", fontSize: "14px" }}>
                {editingDocument.projectInfo.name}
              </p>
            </div>
          }
          open={!!editingDocument}
          onCancel={() => {
            setEditingDocument(null);
            setEditRequest("");
          }}
          width={900}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                setEditingDocument(null);
                setEditRequest("");
              }}
              disabled={isEditing}
            >
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={() => handleEditSubmit(editRequest)}
              loading={isEditing}
              disabled={!editRequest.trim()}
              style={{ background: useOldConversation ? "#1890ff" : "#52c41a" }}
            >
              {isEditing ? "Editing..." : "Apply Changes"}
            </Button>,
          ]}
        >
          <div style={{ padding: "20px 0" }}> */}
      {/* Conversation Toggle */}
      {/* <ConversationToggle
              projectId={editingDocument.id}
              conversationSummary={conversationManager.getConversationSummary(
                editingDocument.id
              )}
              onToggleChange={handleToggleChange}
              defaultValue={useOldConversation}
            /> */}

      {/* ENHANCED: Edit Instructions with prominent question */}
      {/* <div style={{ marginTop: 20 }}>
              <div
                style={{
                  padding: 16,
                  backgroundColor: "#f0f8ff",
                  borderRadius: 8,
                  border: "2px solid #1890ff",
                  marginBottom: 16,
                }}
              >
                <Typography.Text
                  strong
                  style={{
                    fontSize: "18px",
                    display: "block",
                    marginBottom: 8,
                    color: "#1890ff",
                  }}
                >
                  ❓ What changes would you like to make?
                </Typography.Text>
                <Typography.Text style={{ fontSize: "14px", color: "#666" }}>
                  Describe the specific modifications, additions, or
                  improvements you want to make to this SRS document.
                </Typography.Text>
              </div>
              <Input.TextArea
                value={editRequest}
                onChange={(e) => setEditRequest(e.target.value)}
                placeholder="Example: Add a new user role for 'Manager' with permissions to view reports and approve requests..."
                rows={6}
                style={{ fontSize: "14px" }}
                autoFocus
              />
            </div> */}

      {/* Cost Estimate */}
      {/* <div
              style={{
                marginTop: 16,
                padding: 12,
                backgroundColor: useOldConversation ? "#f0f8ff" : "#f6ffed",
                borderRadius: 6,
                border: `1px solid ${
                  useOldConversation ? "#d6e4ff" : "#d9f7be"
                }`,
              }}
            >
              <Typography.Text
                style={{
                  fontSize: "12px",
                  color: useOldConversation ? "#1890ff" : "#52c41a",
                }}
              >
                💰 <strong>Estimated Cost:</strong>{" "}
                {useOldConversation ? "$0.15-$0.45" : "$0.03-$0.12"} •
                <strong> Quality:</strong>{" "}
                {useOldConversation
                  ? "Context-aware editing"
                  : "Simple text editing"}
              </Typography.Text>
            </div>
          </div>
        </Modal>
      )} */}

      {/* Document Viewer Modal */}
      {selectedDocument && (
        <SRSDocumentViewer
          document={selectedDocument}
          onSave={handleSaveDocument}
          onClose={() => setSelectedDocument(null)}
          editable={true}
        />
      )}
    </div>
  );
};

export default History;
