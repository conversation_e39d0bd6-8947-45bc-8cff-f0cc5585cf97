import { useState, useCallback, useEffect } from "react";
import { Form, message } from "antd";
import { AnimatePresence, motion } from "framer-motion";
import { useSearchParams, useNavigate } from "react-router-dom";

// Import AI generation components
import { useSRSGeneration } from "../hooks/useSRSGeneration";
import SRSDocumentViewer from "../components/SRS/SRSDocumentViewer";
import AdditionalQuestionsModal from "../components/AdditionalQuestionsModal";
import SaveProgressModal from "../components/SRS/SaveProgressModal";
import conversationManager from "../services/conversationManager";
import { useSRSHistory } from "../hooks/useLocalStorage";

// Import step components
import Step1BasicInfo from "../components/SRS/Steps/Step1BasicInfo";
import Step2PlatformDeployment from "../components/SRS/Steps/Step2PlatformDeployment";
import Step3TechnologyStack from "../components/SRS/Steps/Step3TechnologyStack";
import Step4UserRoles from "../components/SRS/Steps/Step4UserRoles";
// import Step5FunctionalModules from "../components/SRS/Steps/Step5FunctionalModules";
// import Step6Integrations from "../components/SRS/Steps/Step6Integrations";
import Step7DevelopmentLifecycle from "../components/SRS/Steps/Step7DevelopmentLifecycle";
import Step8TeamComposition from "../components/SRS/Steps/Step8TeamComposition";

// Import navigation components
import ProgressSteps from "../components/SRS/ProgressSteps";
import NavigationButtons from "../components/SRS/NavigationButtons";
import { convertFlatToStepWise, srsSteps } from "../utils/constant";
import { sanitizeData } from "../utils/function";

const Generate = () => {
  // URL parameters and navigation
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Check URL parameters for different modes
  const editDocumentId = searchParams.get("edit");
  const resumeDocumentId = searchParams.get("resume");
  const isEditMode = !!editDocumentId;
  const isResumeMode = !!resumeDocumentId;
  const isFreshStart = !isEditMode && !isResumeMode;

  console.log("🔗 Navigation Mode:", {
    editDocumentId,
    resumeDocumentId,
    isEditMode,
    isResumeMode,
    isFreshStart,
  });

  // Form instance
  const [form] = Form.useForm();

  // FIXED: Move state declarations before functions that use them
  // Step management
  const [currentStep, setCurrentStep] = useState(0);

  // SOLUTION: Form data state management
  // AllFormData stores step-wise format: {1: {projectName: "..."}, 2: {platforms: [...]}, ...}
  // localStorage stores flat format: {projectName: "...", platforms: [...], currentStep: 0, ...}
  // Form expects flat format for setFieldsValue()
  const [AllFormData, setAllFormData] = useState({});

  console.log("📊 AllFormData (step-wise):", AllFormData);
  console.log(
    "📊 localStorage (flat):",
    JSON.parse(localStorage.getItem("srs_form_data") || "{}")
  );

  // AI generation state
  const [showQuestionsModal, setShowQuestionsModal] = useState(false);
  const [additionalQuestions, setAdditionalQuestions] = useState([]);
  const [questionsMessage, setQuestionsMessage] = useState("");
  // const [currentFormData, setCurrentFormData] = useState(null);
  const [shouldRestoreFormData, setShouldRestoreFormData] = useState(false);

  // GLOBAL SOLUTION: Centralized modal state management
  const [showSaveProgressModal, setShowSaveProgressModal] = useState(false);
  const [isSavingProgress, setIsSavingProgress] = useState(false);
  const [modalAlreadyShown, setModalAlreadyShown] = useState(false); // Prevent duplicate modals
  const [isProcessingModalAction, setIsProcessingModalAction] = useState(false); // Prevent re-triggers

  // Use SRS history hook
  const { saveInProgress, completeInProgressSRS } = useSRSHistory();

  // Use AI generation hook
  const {
    isGenerating,
    generationProgress,
    generationStep,
    generatedSRS,
    generateSRS,
    resetGeneration,
    aiConfigured,
  } = useSRSGeneration();

  // SOLUTION: Handle different navigation modes and data loading
  useEffect(() => {
    console.log("🔄 Data loading useEffect triggered");

    // SOLUTION: Handle different modes based on URL parameters
    if (isFreshStart) {
      console.log("🆕 Fresh start mode - clearing any existing data");
      // Clear localStorage for fresh start
      localStorage.removeItem("srs_form_data");
      form.resetFields();
      setAllFormData({});
      setCurrentStep(0);
      return;
    }

    if (isEditMode) {
      console.log("✏️ Edit mode - loading document:", editDocumentId);
      // Load specific document data for editing
      loadDocumentForEdit(editDocumentId);
      return;
    }

    if (isResumeMode) {
      console.log("▶️ Resume mode - loading in-progress:", resumeDocumentId);
      // Load in-progress document data
      loadDocumentForResume(resumeDocumentId);
      return;
    }

    // Fallback: Load any existing localStorage data (backward compatibility)
    console.log("🔄 Fallback mode - checking localStorage");
    const currentForm = localStorage.getItem("srs_form_data");
    if (currentForm) {
      try {
        const parsedData = JSON.parse(currentForm);

        // Check if data is in step-wise format {1: {}, 2: {}, ...} or flat format
        const isStepWiseFormat = Object.keys(parsedData).some(
          (key) =>
            !isNaN(key) &&
            typeof parsedData[key] === "object" &&
            parsedData[key] !== null
        );

        if (isStepWiseFormat) {
          // Convert step-wise format to flat format for form
          const flatData = {};
          Object.values(parsedData).forEach((stepData) => {
            if (typeof stepData === "object" && stepData !== null) {
              Object.assign(flatData, stepData);
            }
          });

          // Set form with flat data
          form.setFieldsValue(flatData);

          // Keep AllFormData in step-wise format
          setAllFormData(parsedData);

          // Update localStorage to flat format for consistency
          localStorage.setItem(
            "srs_form_data",
            JSON.stringify({
              ...flatData,
              currentStep: parsedData.currentStep || 0,
              savedAt: parsedData.savedAt || new Date().toISOString(),
            })
          );
        } else {
          // Data is already in flat format
          form.setFieldsValue(parsedData);

          // Convert flat data to step-wise format for AllFormData
          const stepWiseData = convertFlatToStepWise(parsedData);
          setAllFormData(stepWiseData);
        }

        // Set current step if available
        if (parsedData.currentStep !== undefined) {
          setCurrentStep(parsedData.currentStep);
        }
      } catch (error) {
        console.error("Error parsing localStorage data:", error);
        // Reset if data is corrupted
        localStorage.removeItem("srs_form_data");
      }
    }
  }, []);

  // SOLUTION: Helper functions for loading document data
  const loadDocumentForEdit = useCallback(
    (documentId) => {
      try {
        const historyData = JSON.parse(
          localStorage.getItem("srs_history") || "[]"
        );
        const document = historyData.find((doc) => doc.id === documentId);

        if (document && document.formData) {
          console.log("📄 Loading document for edit:", document);

          // Load the saved form data
          const formData = document.formData;
          form.setFieldsValue(formData);

          // Convert to step-wise format for AllFormData
          const stepWiseData = convertFlatToStepWise(formData);
          setAllFormData(stepWiseData);

          // Set current step
          if (formData.currentStep !== undefined) {
            setCurrentStep(formData.currentStep);
          }

          // Update localStorage with the loaded data
          localStorage.setItem("srs_form_data", JSON.stringify(formData));

          console.log("✅ Document loaded for editing");
        } else {
          console.warn("⚠️ Document not found for editing:", documentId);
          message.warning("Document not found. Starting fresh.");
        }
      } catch (error) {
        console.error("❌ Error loading document for edit:", error);
        message.error("Failed to load document. Starting fresh.");
      }
    },
    [form]
  );

  const loadDocumentForResume = useCallback(
    (documentId) => {
      try {
        const historyData = JSON.parse(
          localStorage.getItem("srs_history") || "[]"
        );
        const document = historyData.find((doc) => doc.id === documentId);

        if (
          document &&
          document.formData &&
          document.metadata?.status === "in-progress"
        ) {
          console.log("▶️ Loading in-progress document:", document);

          // Load the saved form data
          const formData = document.formData;
          form.setFieldsValue(formData);

          // Convert to step-wise format for AllFormData
          const stepWiseData = convertFlatToStepWise(formData);
          setAllFormData(stepWiseData);

          // Set current step
          if (formData.currentStep !== undefined) {
            setCurrentStep(formData.currentStep);
          }

          // Update localStorage with the loaded data
          localStorage.setItem("srs_form_data", JSON.stringify(formData));

          console.log("✅ In-progress document loaded for resume");
        } else {
          console.warn("⚠️ In-progress document not found:", documentId);
          message.warning("In-progress document not found. Starting fresh.");
        }
      } catch (error) {
        console.error("❌ Error loading document for resume:", error);
        message.error("Failed to load document. Starting fresh.");
      }
    },
    [form]
  );

  // Helper function to convert flat data to step-wise format

  // SOLUTION: Unified form data sync function for step components
  const syncFormData = useCallback(
    (stepNumber, fieldKey, fieldValue) => {
      // Update step-wise state
      setAllFormData((prev) => ({
        ...prev,
        [stepNumber]: {
          ...prev[stepNumber],
          [fieldKey]: fieldValue,
        },
      }));

      // Update form field
      form.setFieldValue(fieldKey, fieldValue);

      // Update localStorage in flat format immediately
      try {
        const currentLocalStorage = localStorage.getItem("srs_form_data");
        const currentData = currentLocalStorage
          ? JSON.parse(currentLocalStorage)
          : {};

        const updatedData = {
          ...currentData,
          [fieldKey]: fieldValue,
          currentStep,
          savedAt: new Date().toISOString(),
        };

        localStorage.setItem("srs_form_data", JSON.stringify(updatedData));
        console.log(`✅ Synced field ${fieldKey} across all data sources`);
      } catch (error) {
        console.warn("⚠️ Failed to sync to localStorage:", error);
      }
    },
    [form, currentStep]
  );

  // SOLUTION: Safe form data collection with step-wise to flat conversion
  const getAllFormData = useCallback(() => {
    try {
      // STEP 1: Get current form values (most up-to-date)
      const currentFormValues = form.getFieldsValue();

      // STEP 2: Get saved form data from localStorage
      const savedFormData = localStorage.getItem("srs_form_data");
      const parsedSaved = savedFormData ? JSON.parse(savedFormData) : {};

      // STEP 3: Convert AllFormData (step-wise) to flat format
      const flatFromStepWise = {};
      if (AllFormData && typeof AllFormData === "object") {
        Object.values(AllFormData).forEach((stepData) => {
          if (typeof stepData === "object" && stepData !== null) {
            Object.assign(flatFromStepWise, stepData);
          }
        });
      }

      // STEP 4: Merge all data sources (priority: current form > step-wise > saved)
      const completeFormData = {
        ...parsedSaved, // Saved data (lowest priority)
        ...flatFromStepWise, // Step-wise data (medium priority)
        ...currentFormValues, // Current form values (highest priority)
        currentStep,
        savedAt: new Date().toISOString(),
      };

      return completeFormData;
    } catch (error) {
      console.error("❌ Error collecting complete form data:", error);
      return {
        ...form.getFieldsValue(),
        currentStep,
        savedAt: new Date().toISOString(),
      };
    }
  }, [form, currentStep, AllFormData]);

  // CENTRALIZED SAVE FUNCTION - Called on both Next Step and Save Progress
  // SOLUTION: Always saves in flat format to localStorage for consistency
  const saveFormDataToLocalStorage = useCallback(() => {
    try {
      // Use centralized function to get ALL form data (already in flat format)
      const completeFormData = getAllFormData();

      // Remove undefined/null values to reduce size
      const cleanData = Object.fromEntries(
        Object.entries(completeFormData).filter(
          ([, value]) => value !== undefined && value !== null && value !== ""
        )
      );

      // IMPORTANT: Ensure localStorage always stores flat format
      const flatData = {
        ...cleanData,
        currentStep,
        savedAt: new Date().toISOString(),
      };

      // Save to localStorage in flat format
      const dataString = JSON.stringify(flatData);
      localStorage.setItem("srs_form_data", dataString);

      console.log(
        "✅ Saved to localStorage (flat format):",
        Object.keys(flatData)
      );
      return flatData;
    } catch (error) {
      console.error("❌ Failed to save form data to localStorage:", error);
      // Try to save minimal data as fallback
      try {
        const minimalData = {
          currentStep,
          savedAt: new Date().toISOString(),
          projectName: form.getFieldValue("projectName") || "Untitled",
        };
        localStorage.setItem("srs_form_data", JSON.stringify(minimalData));
        return minimalData;
      } catch (fallbackError) {
        console.error("❌ Even minimal save failed:", fallbackError);
        return null;
      }
    }
  }, [getAllFormData, currentStep, form]);

  const handleGenerateSRS = useCallback(
    async (additionalAnswers = null) => {
      // Prevent multiple simultaneous generations
      if (isGenerating) {
        console.warn(
          "SRS generation already in progress, skipping duplicate call"
        );
        return;
      }

      try {
        // SOLUTION: Validate current step fields before generation
        try {
          await form.validateFields();
          console.log("✅ Current step validation passed");
        } catch (validationError) {
          console.warn(
            "⚠️ Validation failed, but proceeding with available data:",
            validationError
          );
        }

        // SOLUTION: Force save current form data to localStorage before generation
        console.log("🔄 Saving current form data before SRS generation...");
        const savedData = saveFormDataToLocalStorage();

        // SOLUTION: Get the most current form data (including unsaved changes)
        const currentFormValues = form.getFieldsValue();

        // SOLUTION: Also get data from AllFormData (step-wise) and flatten it
        const flatFromStepWise = {};
        if (AllFormData && typeof AllFormData === "object") {
          Object.values(AllFormData).forEach((stepData) => {
            if (typeof stepData === "object" && stepData !== null) {
              Object.assign(flatFromStepWise, stepData);
            }
          });
        }

        const completeFormData = {
          ...savedData, // Saved data from localStorage (lowest priority)
          ...flatFromStepWise, // Step-wise data (medium priority)
          ...currentFormValues, // Current form values (highest priority)
          currentStep,
          savedAt: new Date().toISOString(),
        };

        console.log(
          "📋 Complete form data for SRS generation:",
          completeFormData
        );
        console.log("📊 Data sources merged:", {
          savedDataKeys: Object.keys(savedData || {}),
          stepWiseKeys: Object.keys(flatFromStepWise),
          currentFormKeys: Object.keys(currentFormValues),
          finalKeys: Object.keys(completeFormData),
        });

        // Check if AI is configured
        if (!aiConfigured) {
          message.error(
            "AI service is not configured. Please check your API keys in the environment variables."
          );
          return;
        }

        // Start ChatGPT-style interactive SRS generation
        const result = await generateSRS(completeFormData, additionalAnswers);
        console.log(result, "result");

        if (result.success) {
          message.success("SRS document generated successfully!");
          // Document viewer will be shown automatically
          setShowQuestionsModal(false);
          // Clear form data from memory to free up space
          // setCurrentFormData(null);
        } else if (result.needsMoreInfo) {
          // Show additional questions modal
          setAdditionalQuestions(result.questions);
          setQuestionsMessage(result.message);
          setShowQuestionsModal(true);
          // Store project ID for continuation
          // if (result.projectId) {
          //   setCurrentFormData((prev) => ({
          //     ...prev,
          //     projectId: result.projectId,
          //   }));
          // }
          message.info(
            "Additional information required for comprehensive SRS generation"
          );
        } else {
          message.error(`Failed to generate SRS: ${result.error}`);
          // setCurrentFormData(null); // Clear on error
        }
      } catch (error) {
        console.error("Generation error:", error);
        message.error("Failed to generate SRS document. Please try again.");
        // setCurrentFormData(null); // Clear on error
      }
    },
    [
      generateSRS,
      aiConfigured,
      isGenerating,
      currentStep,
      form,
      saveFormDataToLocalStorage,
      AllFormData,
    ]
  );

  // Handle additional questions submission
  const handleAdditionalQuestionsSubmit = useCallback(
    async (answers) => {
      try {
        // Enhanced sanitization to prevent circular JSON issues
        const sanitizedAnswers = sanitizeData(answers);

        // Check if user chose to skip questions
        if (sanitizedAnswers.skipQuestions && sanitizedAnswers.autoAnalyze) {
          console.log(
            "🤖 User chose to skip questions - auto-analyzing project data"
          );
          await handleGenerateSRS(sanitizedAnswers);
          handleModalClose();
          return;
        }

        // Additional safety check - ensure no circular references
        const testSerialization = JSON.stringify(sanitizedAnswers);
        if (testSerialization.length > 100000) {
          // 100KB limit
          console.warn("⚠️ Large answer data detected, truncating...");
          // Keep only essential fields if data is too large
          const essentialAnswers = Object.fromEntries(
            Object.entries(sanitizedAnswers).slice(0, 10) // First 10 answers only
          );
          await handleGenerateSRS(essentialAnswers);
          handleModalClose();
        } else {
          await handleGenerateSRS(sanitizedAnswers);
          handleModalClose();
        }
      } catch (error) {
        console.error("❌ Error processing additional answers:", error);
        // Fallback with minimal data
        await handleGenerateSRS({
          fallback: "Additional answers provided but could not be processed",
        });
      }
    },
    [handleGenerateSRS]
  );

  // Handle modal close
  const handleModalClose = useCallback(() => {
    setShowQuestionsModal(false);
    setAdditionalQuestions([]);
    setQuestionsMessage("");
  }, []);

  // Handle save progress (SOLUTION: Fixed step-wise to flat conversion)
  const handleSaveProgress = useCallback(
    async (progressData) => {
      // Prevent multiple simultaneous saves
      if (isSavingProgress) {
        console.warn("Save already in progress, skipping duplicate call");
        return null;
      }

      try {
        setIsSavingProgress(true);

        // SOLUTION: Use centralized function to get complete form data in flat format
        const completeFormData = getAllFormData();
        console.log("📝 Complete form data for save:", completeFormData);

        // Create complete progress data using the flat form data
        const completeProgressData = {
          formData: completeFormData,
          currentStep: currentStep,
          savedAt: new Date().toISOString(),
          ...progressData,
        };

        const savedDocument = saveInProgress(completeProgressData);

        if (savedDocument) {
          // Add the document ID to form data
          completeProgressData.formData.id = savedDocument.id;

          message.success(
            `Progress saved! You can continue from step ${
              currentStep + 1
            } later.`
          );

          // IMPORTANT: Update localStorage to match saved progress (flat format)
          try {
            const formDataForLocalStorage = {
              ...completeProgressData.formData,
              currentStep: completeProgressData.currentStep,
              savedAt: completeProgressData.savedAt,
              progressSaved: true, // Flag to indicate this was saved
            };

            const sanitizedFormData = sanitizeData(formDataForLocalStorage);
            localStorage.setItem(
              "srs_form_data",
              JSON.stringify(sanitizedFormData)
            );
            console.log(
              "✅ Updated localStorage to match saved progress (flat format)"
            );
          } catch (error) {
            console.warn("⚠️ Failed to update localStorage:", error);
          }

          return savedDocument;
        } else {
          throw new Error("Failed to save progress document");
        }
      } catch (error) {
        console.error("❌ Save progress error:", error);
        message.error(`Failed to save progress: ${error.message}`);
        return null;
      } finally {
        setIsSavingProgress(false);
      }
    },
    [saveInProgress, isSavingProgress, getAllFormData, currentStep]
  );

  // GLOBAL SOLUTION: Handle save progress modal actions with complete state reset
  const handleSaveAndStartNew = useCallback(async () => {
    // Prevent multiple simultaneous actions
    if (isProcessingModalAction) {
      return;
    }

    try {
      setIsProcessingModalAction(true); // Lock to prevent re-triggers

      // SAVE FORM DATA TO LOCALSTORAGE FIRST
      const savedFormData = saveFormDataToLocalStorage();
      console.log(savedFormData, "savedFormData");
      if (!savedFormData) {
        message.error("Failed to save current progress");
        return;
      }

      const progressData = {
        formData: savedFormData,
        currentStep,
      };

      await handleSaveProgress(progressData);

      // GLOBAL SOLUTION: Complete state reset for new SRS
      form.resetFields();
      setCurrentStep(0);

      // CRITICAL: Clear localStorage completely BEFORE closing modal
      localStorage.removeItem("srs_form_data");

      // CRITICAL: Remove in-progress SRS from history to prevent re-detection
      const historyData = JSON.parse(
        localStorage.getItem("srs_history") || "[]"
      );
      const filteredHistory = historyData.filter(
        (doc) => doc?.metadata?.status !== "in-progress"
      );
      localStorage.setItem("srs_history", JSON.stringify(filteredHistory));

      // Reset generation state
      resetGeneration();

      // Close modal and reset flags
      setShowSaveProgressModal(false);
      setModalAlreadyShown(false); // Reset for future use

      message.success(
        "New SRS generation started. Previous progress saved to history."
      );
    } catch (error) {
      console.error("Error saving and starting new:", error);
      message.error("Failed to save progress. Please try again.");
    } finally {
      setIsProcessingModalAction(false); // Always unlock
    }
  }, [
    form,
    saveFormDataToLocalStorage,
    currentStep,
    handleSaveProgress,
    resetGeneration,
    isProcessingModalAction,
  ]);

  // const handleContinueCurrent = useCallback(() => {
  //   setShowSaveProgressModal(false);
  //   setModalAlreadyShown(true); // Prevent modal from showing again
  //   setShouldRestoreFormData(true); // ONLY restore when user chooses to continue
  //   message.info("Continuing with current SRS generation.");
  // }, []);

  // const handleDiscardAndStartNew = useCallback(() => {
  //   // Prevent multiple simultaneous actions
  //   if (isProcessingModalAction) {
  //     return;
  //   }

  //   try {
  //     setIsProcessingModalAction(true); // Lock to prevent re-triggers

  //     // GLOBAL SOLUTION: Reset form for new SRS
  //     form.resetFields();
  //     setCurrentStep(0);

  //     // CRITICAL: Clear localStorage completely
  //     localStorage.removeItem("srs_form_data");

  //     // CRITICAL: Remove any in-progress SRS from history to prevent re-detection
  //     const historyData = JSON.parse(
  //       localStorage.getItem("srs_history") || "[]"
  //     );
  //     const filteredHistory = historyData.filter(
  //       (doc) => doc?.metadata?.status !== "in-progress"
  //     );
  //     localStorage.setItem("srs_history", JSON.stringify(filteredHistory));

  //     // Reset generation state
  //     resetGeneration();

  //     // Close modal and reset flags
  //     setShowSaveProgressModal(false);
  //     setModalAlreadyShown(false); // Reset for future use

  //     message.warning(
  //       "Previous progress discarded. Starting fresh SRS generation."
  //     );
  //   } catch (error) {
  //     console.error("Error discarding progress:", error);
  //     message.error("Failed to discard progress. Please try again.");
  //   } finally {
  //     setIsProcessingModalAction(false); // Always unlock
  //   }
  // }, [form, resetGeneration, isProcessingModalAction]);

  // Memoized handler functions for better performance
  const handleNext = useCallback(async () => {
    try {
      await form.validateFields();

      // SAVE FORM DATA TO LOCALSTORAGE ON NEXT STEP
      const savedData = saveFormDataToLocalStorage();
      console.log(savedData, "saved data, handleNext");
      if (savedData) {
        console.log("✅ Step data saved before navigation:", {
          fromStep: currentStep,
          toStep: currentStep + 1,
          savedFields: Object.keys(savedData),
        });
      }

      if (currentStep < srsSteps?.length - 1) {
        const nextStep = currentStep + 1;
        setCurrentStep(nextStep);
      } else {
        // Generate SRS
        handleGenerateSRS();
      }
    } catch {
      message.error("Please fill in all required fields before proceeding.");
    }
  }, [currentStep, form, handleGenerateSRS, saveFormDataToLocalStorage]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
    }
  }, [currentStep]);

  const handleStatusBarClick = useCallback(
    (index) => {
      console.log(index, "index", currentStep);
      if (currentStep >= 0) {
        setCurrentStep(index);
      }
    },
    [currentStep]
  );

  // Render current step with unified sync function
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <Step1BasicInfo
            form={form}
            setAllFormData={setAllFormData}
            AllFormData={AllFormData}
            syncFormData={syncFormData}
          />
        );
      case 1:
        return (
          <Step2PlatformDeployment
            form={form}
            setAllFormData={setAllFormData}
            AllFormData={AllFormData}
            syncFormData={syncFormData}
          />
        );
      case 2:
        return (
          <Step3TechnologyStack
            form={form}
            setAllFormData={setAllFormData}
            AllFormData={AllFormData}
            syncFormData={syncFormData}
          />
        );
      case 3:
        return (
          <Step4UserRoles
            form={form}
            setAllFormData={setAllFormData}
            AllFormData={AllFormData}
            syncFormData={syncFormData}
          />
        );
      // case 4:
      //   return (
      //     <Step5FunctionalModules
      //       functionalModules={functionalModules}
      //       addFunctionalModule={addFunctionalModule}
      //       removeFunctionalModule={removeFunctionalModule}
      //       updateFunctionalModule={updateFunctionalModule}
      //       form={form}
      //       setAllFormData={setAllFormData}
      //     />
      //   );
      // case 4:
      //   return (
      // <Step6Integrations form={form} setAllFormData={setAllFormData} />
      //   );
      case 4:
        return (
          <Step7DevelopmentLifecycle
            form={form}
            setAllFormData={setAllFormData}
            AllFormData={AllFormData}
            syncFormData={syncFormData}
          />
        );
      case 5:
        return (
          <Step8TeamComposition
            form={form}
            setAllFormData={setAllFormData}
            AllFormData={AllFormData}
            syncFormData={syncFormData}
          />
        );
      default:
        return (
          <Step1BasicInfo
            form={form}
            setAllFormData={setAllFormData}
            AllFormData={AllFormData}
            syncFormData={syncFormData}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Enhanced Progress Steps Component */}
          <ProgressSteps
            handleStatusBarClick={handleStatusBarClick}
            steps={srsSteps}
            currentStep={currentStep}
          />

          {/* Form Content */}
          <Form form={form} layout="vertical" className="mt-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderCurrentStep()}
              </motion.div>
            </AnimatePresence>
          </Form>

          {/* Enhanced Navigation Buttons Component */}
          <div className="mt-8">
            <NavigationButtons
              currentStep={currentStep}
              totalSteps={srsSteps?.length}
              isGenerating={isGenerating}
              generationProgress={generationProgress}
              generationStep={generationStep}
              onPrevious={handlePrevious}
              onNext={handleNext}
              onGenerate={handleGenerateSRS}
              onSave={() => message.success("Progress saved successfully!")}
              onSaveProgress={handleSaveProgress}
              getAllFormData={getAllFormData}
              form={form}
              aiConfigured={aiConfigured}
            />
          </div>
        </motion.div>
      </div>

      {/* SRS Document Viewer */}
      {generatedSRS && (
        <SRSDocumentViewer
          document={generatedSRS}
          onSave={() => {
            // Handle document save
            message.success("Document saved successfully!");
          }}
          onClose={() => {
            resetGeneration();
          }}
          editable={true}
        />
      )}

      {/* ChatGPT-style Additional Questions Modal */}
      {showQuestionsModal && (
        <AdditionalQuestionsModal
          visible={showQuestionsModal}
          onCancel={handleModalClose}
          onSubmit={handleAdditionalQuestionsSubmit}
          questions={additionalQuestions}
          message={questionsMessage}
          loading={isGenerating}
        />
      )}

      {/* Save Progress Modal */}
      {/* <SaveProgressModal
        visible={showSaveProgressModal}
        onCancel={() => setShowSaveProgressModal(false)}
        onSaveAndStartNew={handleSaveAndStartNew}
        onContinueCurrent={handleContinueCurrent}
        onDiscardAndStartNew={handleDiscardAndStartNew}
        currentStep={currentStep}
        totalSteps={srsSteps?.length || 8}
        projectName={form?.getFieldValue("projectName") || "Untitled Project"}
        loading={isSavingProgress}
      /> */}
    </div>
  );
};

export default Generate;
