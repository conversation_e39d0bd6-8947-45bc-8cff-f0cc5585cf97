import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import { useSRSHistory } from './useLocalStorage';

export const useNavigationGuard = () => {
  const navigate = useNavigate();
  const { saveInProgress } = useSRSHistory();
  const [showSaveProgressModal, setShowSaveProgressModal] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [isSavingProgress, setIsSavingProgress] = useState(false);

  // Check localStorage directly to avoid dependency loops
  // const checkHasInProgressSRS = useCallback(() => {
  //   try {
  //     const historyData = JSON.parse(localStorage.getItem('srs_history') || '[]');
  //     return historyData.some(doc => doc?.metadata?.status === 'in-progress');
  //   } catch {
  //     return false;
  //   }
  // }, []);

  // Check if navigation should be guarded
  // const shouldGuardNavigation = useCallback((targetPath) => {
  //   // Only guard navigation to /generate if there's an in-progress SRS
  //   return targetPath === '/generate' && checkHasInProgressSRS();
  // }, [checkHasInProgressSRS]);

  // Navigate with guard check
  const guardedNavigate = useCallback((targetPath) => {
    // console.log(shouldGuardNavigation(targetPath), "🔒 Guarding navigation to:", targetPath);
    // if (shouldGuardNavigation(targetPath)) {
    //   // Show save progress modal
    //   setPendingNavigation(targetPath);
    //   setShowSaveProgressModal(true);
    // } else {
    // Navigate normally
    navigate(targetPath);
    // }
  }, [
    // shouldGuardNavigation, 
    navigate]);

  // Get current in-progress SRS info (FIXED - Direct localStorage access)
  const getInProgressInfo = useCallback(() => {
    try {
      const historyData = JSON.parse(localStorage.getItem('srs_history') || '[]');
      const inProgressSRS = historyData.find(doc => doc?.metadata?.status === 'in-progress');

      if (!inProgressSRS) return null;

      return {
        projectName: inProgressSRS.projectInfo.name,
        currentStep: inProgressSRS.metadata.currentStep,
        totalSteps: inProgressSRS.metadata.totalSteps,
        completionPercentage: inProgressSRS.metadata.completionPercentage
      };
    } catch {
      return null;
    }
  }, []);

  // Handle save and navigate
  const handleSaveAndNavigate = useCallback(async () => {
    try {
      setIsSavingProgress(true);

      // Get current form data from localStorage
      const savedFormData = localStorage.getItem("srs_form_data");
      if (savedFormData) {
        const formData = JSON.parse(savedFormData);

        // Save as in-progress
        const progressData = {
          formData: formData,
          currentStep: formData.currentStep || 0,
          userRoles: formData.userRoles || [],
          functionalModules: formData.functionalModules || [],
          teamMembers: formData.teamMembers || {},
          selectedPlatforms: formData.selectedPlatforms || [],
        };

        saveInProgress(progressData);
        message.success('Progress saved successfully!');
      }

      // Clear localStorage
      localStorage.removeItem("srs_form_data");

      // Navigate to target
      if (pendingNavigation) {
        navigate(pendingNavigation);
      }

      // Close modal
      setShowSaveProgressModal(false);
      setPendingNavigation(null);

    } catch (error) {
      console.error('Save progress error:', error);
      message.error('Failed to save progress. Please try again.');
    } finally {
      setIsSavingProgress(false);
    }
  }, [saveInProgress, navigate, pendingNavigation]);

  // Handle continue current
  const handleContinueCurrent = useCallback(() => {
    setShowSaveProgressModal(false);
    setPendingNavigation(null);

    // If trying to navigate to /generate, just stay there
    if (pendingNavigation === '/generate') {
      navigate('/generate');
    }

    message.info('Continuing with current SRS generation.');
  }, [navigate, pendingNavigation]);

  // Handle discard and navigate
  const handleDiscardAndNavigate = useCallback(() => {
    // Clear localStorage
    localStorage.removeItem("srs_form_data");

    // Navigate to target
    if (pendingNavigation) {
      navigate(pendingNavigation);
    }

    // Close modal
    setShowSaveProgressModal(false);
    setPendingNavigation(null);

    message.warning('Previous progress discarded.');
  }, [navigate, pendingNavigation]);

  // Handle modal close
  const handleModalClose = useCallback(() => {
    setShowSaveProgressModal(false);
    setPendingNavigation(null);
  }, []);

  return {
    // Navigation functions
    guardedNavigate,
    // shouldGuardNavigation,

    // Modal state
    showSaveProgressModal,
    setShowSaveProgressModal,
    isSavingProgress,

    // In-progress info
    getInProgressInfo,
    // checkHasInProgressSRS,

    // Modal handlers
    handleSaveAndNavigate,
    handleContinueCurrent,
    handleDiscardAndNavigate,
    handleModalClose,

    // Current navigation target
    pendingNavigation
  };
};
