import conversationManager from './conversationManager.js';

class AIService {
  constructor() {
    this.openaiKey = import.meta.env.VITE_OPENAI_API_KEY;
    this.geminiKey = import.meta.env.VITE_GEMINI_API_KEY;
    this.provider = import.meta.env.VITE_AI_PROVIDER || 'openai';
    this.currentProjectId = null;

    // ENTERPRISE TOKEN MANAGEMENT SYSTEM
    this.tokenManager = {
      // Rate limiting tracking
      lastRequestTime: 0,
      tokensUsedThisMinute: 0,
      requestQueue: [],

      // Token limits per model
      limits: {
        'gpt-4': { maxTokens: 8192, rateLimit: 30000, costPer1K: 0.045 },
        'gpt-4o': { maxTokens: 8192, rateLimit: 30000, costPer1K: 0.01 },
        'gpt-4-turbo': { maxTokens: 128000, rateLimit: 30000, costPer1K: 0.02 },
        'gpt-3.5-turbo': { maxTokens: 4096, rateLimit: 90000, costPer1K: 0.0015 }
      },

      // Smart batching for multi-step generation
      batchMode: false,
      batchDelay: 5000, // FIXED: Increased to 5 seconds between requests
      maxRetries: 3,

      // FIXED: Add conversation size limits
      maxConversationTokens: 15000, // Maximum tokens for entire conversation
      maxPromptTokens: 10000 // Maximum tokens for a single prompt
    };
  }

  // ENTERPRISE TOKEN MANAGEMENT METHODS

  // Check if we can make a request without hitting rate limits
  async checkRateLimit(estimatedTokens, model = 'gpt-4') {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Reset counter if more than a minute has passed
    if (this.tokenManager.lastRequestTime < oneMinuteAgo) {
      this.tokenManager.tokensUsedThisMinute = 0;
    }

    const limit = this.tokenManager.limits[model]?.rateLimit || 30000;

    // FIXED: More aggressive rate limiting - use 60% of limit for safety
    const safeLimit = Math.floor(limit * 0.6);
    const wouldExceed = this.tokenManager.tokensUsedThisMinute + estimatedTokens > safeLimit;

    if (wouldExceed) {
      const waitTime = 60000 - (now - this.tokenManager.lastRequestTime);
      console.log(`⏳ Rate limit protection: Waiting ${Math.ceil(waitTime / 1000)}s before next request (${this.tokenManager.tokensUsedThisMinute}/${safeLimit} tokens used)`);
      await new Promise(resolve => setTimeout(resolve, waitTime + 3000)); // FIXED: Extra 3s buffer
      this.tokenManager.tokensUsedThisMinute = 0;
    }

    return true;
  }

  // Update token usage tracking
  updateTokenUsage(tokens, model = 'gpt-4') {
    this.tokenManager.tokensUsedThisMinute += tokens;
    this.tokenManager.lastRequestTime = Date.now();

    const cost = (tokens / 1000) * (this.tokenManager.limits[model]?.costPer1K || 0.045);
    console.log(`📊 Token usage: ${tokens} tokens, ~$${cost.toFixed(4)} cost`);

    return cost;
  }

  // Smart model selection based on content and rate limits
  selectOptimalModelForContent(conversation, contentType = 'general') {
    const estimatedTokens = conversationManager.estimateTokens(conversation);

    // FIXED: For SRS generation, prioritize quality over cost with proper token limits
    if (contentType === 'srs_generation') {
      if (estimatedTokens > 4000) {
        return 'gpt-4-turbo'; // Best for long, complex documents (reduced from 6000)
      } else if (estimatedTokens > 2500) {
        return 'gpt-4o'; // Optimized for detailed responses (reduced from 3000)
      } else {
        return 'gpt-4'; // Standard quality
      }
    }

    // For general use, balance cost and quality
    if (estimatedTokens > 7000) {
      return 'gpt-4-turbo';
    } else if (estimatedTokens > 4000) {
      return 'gpt-4o';
    } else {
      return 'gpt-4';
    }
  }

  // Sanitize data to remove circular references and React elements
  // ENHANCED to prevent all circular reference issues
  sanitizeData(data, seen = new WeakSet()) {
    if (data === null || data === undefined) {
      return data;
    }

    // Handle primitive types
    if (typeof data !== 'object') {
      return data;
    }

    // Check for circular references
    if (seen.has(data)) {
      return '[Circular Reference]';
    }
    seen.add(data);

    // Handle arrays
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item, seen));
    }

    // Handle objects
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      // Skip React-specific properties, DOM elements, and functions
      if (
        key.startsWith('__react') ||
        key.startsWith('_react') ||
        key.startsWith('_owner') ||
        key === 'nativeEvent' ||
        key === 'currentTarget' ||
        key === 'target' ||
        key === 'ref' ||
        typeof value === 'function' ||
        (value && typeof value === 'object' && value.constructor &&
          (value.constructor.name.includes('HTML') ||
            value.constructor.name.includes('Element') ||
            value.constructor.name.includes('Node') ||
            value.constructor.name.includes('Event')))
      ) {
        continue;
      }

      // Recursively sanitize nested objects
      try {
        sanitized[key] = this.sanitizeData(value, seen);
      } catch {
        // Skip properties that cause any issues
        console.warn(`Skipping property ${key} due to serialization error`);
        continue;
      }
    }

    return sanitized;
  }

  // Helper method to safely format integrations without circular references
  formatIntegrations(integrations) {
    if (!integrations || typeof integrations !== 'object') {
      return 'None specified';
    }

    try {
      const formatted = Object.entries(integrations)
        .filter(([, value]) => value && value !== '' && value !== false)
        .map(([key, value]) => {
          if (typeof value === 'boolean') {
            return key;
          } else if (typeof value === 'string') {
            return `${key}: ${value}`;
          } else if (Array.isArray(value)) {
            return `${key}: ${value.join(', ')}`;
          } else {
            return key; // Fallback for complex objects
          }
        })
        .join(', ');

      return formatted || 'None specified';
    } catch (error) {
      console.warn('Error formatting integrations:', error);
      return 'Integration data available but could not be formatted';
    }
  }

  // Helper method to safely format additional answers without circular references
  formatAdditionalAnswers(answers) {
    if (!answers || typeof answers !== 'object') {
      return 'No additional answers provided';
    }

    try {
      const formatted = Object.entries(answers)
        .filter(([, value]) => value && value !== '' && value !== null && value !== undefined)
        .map(([key, value]) => {
          if (typeof value === 'string') {
            return `${key}: ${value}`;
          } else if (Array.isArray(value)) {
            return `${key}: ${value.join(', ')}`;
          } else if (typeof value === 'object') {
            return `${key}: [Complex data provided]`;
          } else {
            return `${key}: ${String(value)}`;
          }
        })
        .join('\n');

      return formatted || 'No additional answers provided';
    } catch (error) {
      console.warn('Error formatting additional answers:', error);
      return 'Additional answers provided but could not be formatted';
    }
  }

  // MEMORY-PERSISTENT 3-STEP SRS GENERATION SYSTEM
  // Each step stores conversation memory for future editing
  // OPTIMIZED to prevent memory leaks and API timeouts
  async generateSRS(formData, additionalAnswers = null, projectId = null) {
    try {
      this.updateProgress?.(10, 'Initializing memory-persistent SRS generation...');

      // Clean up old conversations before starting new generation
      if (!projectId) {
        conversationManager.cleanupOldConversations(7); // Clean conversations older than 7 days
      }

      // Create or get existing project
      if (!projectId) {
        projectId = `srs_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        conversationManager.initializeProject(projectId, formData);
      }
      this.currentProjectId = projectId;

      // Step 1: Context Initialization & Validation
      if (!additionalAnswers) {
        this.updateProgress?.(20, 'Step 1: Analyzing project data and validating requirements...');
        return await this.step1_InitializeAndValidate(projectId, formData);
      }

      // Step 2: Process Additional Answers
      if (additionalAnswers) {
        this.updateProgress?.(50, 'Step 2: Processing additional information...');
        return await this.step2_ProcessAdditionalAnswers(projectId, additionalAnswers);
      }

      throw new Error('Invalid generation flow');

    } catch (error) {
      console.error('Memory-persistent SRS Generation Failed:', error);

      // Clean up on error to prevent memory leaks
      if (projectId) {
        conversationManager.forceMemoryCleanup();
      }

      throw new Error(`SRS Generation Error: ${error.message}`);
    }
  }

  // EDITING SYSTEM: Toggle between old conversation vs new conversation
  async editSRS(projectId, editRequest, useOldConversation = true) {
    try {
      this.updateProgress?.(10, 'Initializing SRS editing...');

      if (useOldConversation) {
        this.updateProgress?.(30, 'Using old conversation context for editing...');
        return await this.editWithOldConversation(projectId, editRequest);
      } else {
        this.updateProgress?.(30, 'Starting fresh conversation for editing...');
        return await this.editWithNewConversation(projectId, editRequest);
      }

    } catch (error) {
      console.error('SRS Editing Failed:', error);
      throw new Error(`SRS Editing Error: ${error.message}`);
    }
  }

  // Step 1: Initialize and Validate Input with Memory Storage
  async step1_InitializeAndValidate(projectId, formData) {
    // Sanitize form data to prevent circular JSON issues
    const sanitizedFormData = this.sanitizeData(formData);

    // Add comprehensive initial user data to conversation
    conversationManager.addMessage(projectId, 'user',
      `You are an expert Software Requirements Specification (SRS) analyst. Analyze this project data comprehensively and determine what additional information is needed to generate a complete, professional-grade SRS document following IEEE 830 standards.

PROJECT DETAILS PROVIDED:
- Name: ${sanitizedFormData.projectName || 'Not specified'}
- Description: ${sanitizedFormData.projectDescription || 'Not specified'}
- Main Goal: ${sanitizedFormData.mainGoal || 'Not specified'}
- Platforms: ${sanitizedFormData.selectedPlatforms?.join(', ') || 'Not specified'}
- User Roles: ${sanitizedFormData.userRoles?.map(r => `${r.name}: ${r.actions}`).join(', ') || 'Not specified'}
- Modules: ${sanitizedFormData.functionalModules?.map(m => `${m.description} (${m.priority})`).join(', ') || 'Not specified'}
- Team: ${Object.entries(sanitizedFormData.teamMembers || {}).map(([role, count]) => `${role}: ${count}`).join(', ') || 'Not specified'}
- Frontend Tech: ${sanitizedFormData.frontendTech?.join(', ') || 'Not specified'}
- Backend Tech: ${sanitizedFormData.backendTech?.join(', ') || 'Not specified'}
- Database: ${sanitizedFormData.databaseTech?.join(', ') || 'Not specified'}
- Mobile Tech: ${sanitizedFormData.mobileTech?.join(', ') || 'Not specified'}
- Hosting: ${sanitizedFormData.hostingPreference?.join(', ') || 'Not specified'}
- Development Approach: ${sanitizedFormData.developmentApproach?.join(', ') || 'Not specified'}
- Integrations: ${this.formatIntegrations(sanitizedFormData.integrations) || 'Not specified'}

🧠 INTELLIGENT ANALYSIS REQUIREMENTS:
As an expert SRS AI agent, analyze if you have enough information to generate a comprehensive, professional SRS document. Use your extensive industry knowledge to fill gaps automatically.

📋 EVALUATION CRITERIA:
1. **Core Functionality**: What the system does and how users interact with it
2. **User Experience**: Who uses the system and their main tasks
3. **Business Context**: Industry, scale, and business goals
4. **Technical Needs**: Performance, security, and integration requirements
5. **Data Handling**: What information the system stores and processes

🎯 SMART QUESTIONING STRATEGY:
- Ask ONLY essential questions that users can easily answer
- Use simple, non-technical language
- Keep questions short and specific
- Focus on business needs, not technical implementation
- If information is missing, use your expert knowledge to fill gaps

📝 QUESTION GUIDELINES:
- ✅ "How many people will use your system daily?" (NOT "What are your scalability requirements?")
- ✅ "Do users need to log in with passwords?" (NOT "Define authentication mechanisms")
- ✅ "Will you accept payments online?" (NOT "Specify payment gateway integrations")
- ✅ "Do you need reports and analytics?" (NOT "Define reporting requirements")

🚀 AUTO-ANALYSIS CAPABILITY:
When users provide minimal information or skip questions:
1. **Detect Business Domain**: Analyze project description to identify industry
2. **Apply Best Practices**: Use industry-standard requirements and patterns
3. **Fill Technical Gaps**: Add appropriate architecture, security, and performance requirements
4. **Include Compliance**: Add relevant regulatory and security standards
5. **Generate Expert Content**: Create comprehensive SRS using professional knowledge

📊 RESPONSE FORMAT:
If more information needed: {"status": "NEED_MORE_INFO", "questions": [{"id": "unique_id", "question": "simple user-friendly question", "type": "text|textarea|select|multiselect", "required": false, "options": [...]}], "message": "brief explanation"}

If ready to generate: {"status": "READY_TO_GENERATE"}

🎯 GOAL: Generate expert-level SRS documents with minimal user burden. Use your comprehensive knowledge to create professional documentation even with basic input.`, 1);

    // Get conversation for API call
    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Make API call with conversation context
    const response = await this.callGPTWithConversation(conversation);

    // Store AI response in conversation
    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, 1, tokens, cost);

    try {
      const parsedResponse = JSON.parse(response);

      if (parsedResponse.status === 'NEED_MORE_INFO') {
        this.updateProgress?.(30, 'Additional information required...');
        return {
          needsMoreInfo: true,
          questions: parsedResponse.questions,
          message: parsedResponse.message,
          projectId: projectId
        };
      } else if (parsedResponse.status === 'READY_TO_GENERATE') {
        this.updateProgress?.(40, 'All information collected. Ready to generate SRS...');
        return await this.step3_GenerateFinalSRS(projectId);
      }
    } catch {
      // If response is not JSON, treat as needing more info
      return {
        needsMoreInfo: true,
        questions: [
          {
            id: 'general_clarification',
            question: 'Please provide any additional project details or clarifications needed for the SRS.',
            type: 'textarea',
            required: true
          }
        ],
        message: 'Additional information needed for complete SRS generation.',
        projectId: projectId
      };
    }
  }

  // Step 2: Process Additional Answers with Memory Storage
  async step2_ProcessAdditionalAnswers(projectId, additionalAnswers) {
    // Sanitize additional answers to prevent circular JSON issues
    const sanitizedAnswers = this.sanitizeData(additionalAnswers);

    // Check if user chose to skip questions and auto-analyze
    if (sanitizedAnswers.skipQuestions && sanitizedAnswers.autoAnalyze) {
      // Add enhanced skip message with expert-level auto-analysis
      conversationManager.addMessage(projectId, 'user',
        `🚀 USER CHOSE EXPERT AUTO-ANALYSIS: Please use your comprehensive SRS expertise to analyze all provided project information and auto-generate professional-grade requirements.

**🧠 EXPERT ANALYSIS INSTRUCTIONS:**
1. **Business Domain Detection**: Identify the industry and business context from project description
2. **Technology Stack Optimization**: Recommend appropriate technologies based on project needs and scale
3. **Security & Compliance**: Add industry-standard security requirements and relevant compliance (GDPR, HIPAA, PCI DSS, etc.)
4. **Scalability Planning**: Design architecture for projected user load and growth
5. **Performance Standards**: Define appropriate performance metrics and SLAs
6. **Integration Requirements**: Identify likely third-party integrations and APIs needed
7. **User Experience**: Design comprehensive user workflows and interface requirements
8. **Data Architecture**: Plan database schema and data management strategies
9. **DevOps & Deployment**: Include modern CI/CD, monitoring, and deployment requirements
10. **Risk Management**: Identify potential risks and mitigation strategies

**📋 FILL ALL GAPS WITH EXPERT KNOWLEDGE:**
- Use industry best practices and standards
- Apply modern development methodologies (Agile, DevOps, Cloud-Native)
- Include appropriate regulatory and compliance requirements
- Add comprehensive testing and quality assurance requirements
- Design for scalability, security, and maintainability
- Include detailed project management and timeline recommendations

**🎯 GENERATE EXPERT-LEVEL SRS**: Create a comprehensive, professional SRS document that exceeds industry standards using your extensive knowledge base.

Respond with JSON format: {"status": "READY_TO_GENERATE"}`, 2);
    } else {
      // Add user answers to conversation
      conversationManager.addMessage(projectId, 'user',
        `Additional answers provided: ${this.formatAdditionalAnswers(sanitizedAnswers)}

Review these answers and determine if more information is still needed or if we're ready to generate the complete SRS document. Respond with JSON format: {"status": "NEED_MORE_INFO", "questions": [...]} or {"status": "READY_TO_GENERATE"}`, 2);
    }

    // Get conversation for API call
    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Make API call with conversation context
    const response = await this.callGPTWithConversation(conversation);

    // Store AI response in conversation
    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, 2, tokens, cost);

    try {
      const parsedResponse = JSON.parse(response);

      if (parsedResponse.status === 'NEED_MORE_INFO') {
        return {
          needsMoreInfo: true,
          questions: parsedResponse.questions,
          message: parsedResponse.message,
          projectId: projectId
        };
      } else if (parsedResponse.status === 'READY_TO_GENERATE') {
        return await this.step3_GenerateFinalSRS(projectId);
      }
    } catch {
      // If parsing fails, assume ready to generate
      return await this.step3_GenerateFinalSRS(projectId);
    }
  }

  // Step 3: Generate Final SRS with Intelligent Auto-Split System
  async step3_GenerateFinalSRS(projectId) {
    this.updateProgress?.(60, 'Starting enterprise-grade auto-split SRS generation...');

    try {
      // Enable batch mode for multi-step generation
      this.tokenManager.batchMode = true;

      // ENTERPRISE AUTO-SPLIT APPROACH: Generate complete SRS in optimized batches
      // This ensures complete documents while respecting token limits

      // Initialize context management
      const contextManager = this.initializeSRSContext(projectId);

      // STEP 3A: Generate Foundation Sections (Introduction, Overview, User Roles)
      this.updateProgress?.(65, 'Generating foundation sections...');
      const foundationSections = await this.step3A_GenerateFoundationSections(projectId, contextManager);

      // STEP 3B: Generate Functional Requirements (Features, Interfaces, Data)
      this.updateProgress?.(75, 'Generating functional requirements...');
      const functionalSections = await this.step3B_GenerateFunctionalSections(projectId, contextManager);

      // STEP 3C: Generate Technical Specifications (Architecture, Non-Functional, Testing)
      this.updateProgress?.(85, 'Generating technical specifications...');
      const technicalSections = await this.step3C_GenerateTechnicalSections(projectId, contextManager);

      // STEP 3D: Generate Project Management (Team, Timeline, Deployment, Risk)
      this.updateProgress?.(92, 'Generating project management sections...');
      const managementSections = await this.step3D_GenerateManagementSections(projectId, contextManager);

      // STEP 3E: Assemble Complete SRS Document
      this.updateProgress?.(97, 'Assembling complete SRS document...');
      const completeSRS = await this.step3E_AssembleCompleteSRS(projectId, {
        foundation: foundationSections,
        functional: functionalSections,
        technical: technicalSections,
        management: managementSections
      });

      // Mark project as completed
      conversationManager.markProjectCompleted(projectId, completeSRS);

      this.updateProgress?.(100, 'Enterprise-grade auto-split SRS generation completed!');
      return completeSRS;

    } catch (error) {
      console.error('❌ Auto-split SRS generation failed:', error);

      // FALLBACK: If rate limited, use optimized single-call approach
      if (error.message.includes('rate_limit_exceeded')) {
        this.updateProgress?.(70, 'Rate limit detected, switching to fallback generation...');
        return await this.step3_FallbackSingleCallSRS(projectId);
      }

      throw error;
    } finally {
      this.tokenManager.batchMode = false;
    }
  }

  // Initialize SRS Context Management System with Timeline Intelligence
  initializeSRSContext(projectId) {
    const project = conversationManager.getProject(projectId);
    const formData = project?.formData || {};

    return {
      projectId,
      projectData: formData,
      generatedSections: [],
      terminology: new Map(),
      crossReferences: [],
      sectionContext: '',
      totalTokensUsed: 0,
      estimatedCost: 0,
      // Timeline Intelligence Data
      timelineAnalysis: this.analyzeProjectForTimeline(formData)
    };
  }

  // Comprehensive Project Timeline Analysis with Error Handling
  analyzeProjectForTimeline(formData) {
    try {
      return {
        // Project Complexity Analysis
        complexity: this.calculateProjectComplexity(formData),

        // Team Analysis from Step 8
        teamData: this.extractTeamData(formData),

        // Industry Pattern Detection
        industryPattern: this.detectIndustryPattern(formData),

        // Module Complexity Assessment
        moduleComplexity: this.assessModuleComplexity(formData),

        // Technology Stack Impact
        techStackImpact: this.analyzeTechStackComplexity(formData),

        // Integration Complexity
        integrationComplexity: this.analyzeIntegrationComplexity(formData)
      };
    } catch (error) {
      console.warn('⚠️ Timeline analysis error, using defaults:', error);

      // Return safe defaults if analysis fails
      return {
        complexity: { score: 5, level: 'Medium', factors: { modules: 5, userRoles: 2, platforms: 1, integrations: 2 } },
        teamData: { size: 3, experience: 'mid', structure: {}, approach: [], efficiency: 1.0 },
        industryPattern: { industry: 'general', confidence: 0.5, standardTimeline: { medium: '20-32 weeks' }, factors: ['Standard development'] },
        moduleComplexity: { totalModules: 5, complexityBreakdown: [], averageComplexity: 'Medium' },
        techStackImpact: { score: 2, level: 'Medium', technologies: {} },
        integrationComplexity: { score: 1, level: 'Simple', integrations: [], count: 0 }
      };
    }
  }

  // Calculate Project Complexity Score (1-10 scale)
  calculateProjectComplexity(formData) {
    let complexityScore = 1;

    // Module count impact
    const moduleCount = this.countProjectModules(formData);
    complexityScore += Math.min(moduleCount * 0.5, 3);

    // User roles complexity
    const userRoles = formData.userRoles?.length || 1;
    complexityScore += Math.min(userRoles * 0.3, 2);

    // Platform complexity
    const platforms = formData.platforms?.length || 1;
    complexityScore += Math.min(platforms * 0.4, 2);

    // Integration complexity
    const integrations = formData.integrations?.length || 0;
    complexityScore += Math.min(integrations * 0.3, 2);

    return {
      score: Math.min(complexityScore, 10),
      level: complexityScore <= 3 ? 'Simple' : complexityScore <= 6 ? 'Medium' : complexityScore <= 8 ? 'High' : 'Complex',
      factors: {
        modules: moduleCount,
        userRoles: userRoles,
        platforms: platforms,
        integrations: integrations
      }
    };
  }

  // Extract and Analyze Team Data from Step 8
  extractTeamData(formData) {
    // Extract team structure from Step 8 form data
    const teamStructure = formData.teamStructure || {};
    const teamSize = formData.teamSize || 'auto-estimate';
    const teamExperience = formData.teamExperience || 'auto-estimate';
    const developmentApproach = formData.developmentApproach || [];

    return {
      size: this.normalizeTeamSize(teamSize),
      experience: this.normalizeTeamExperience(teamExperience),
      structure: teamStructure,
      approach: developmentApproach,
      efficiency: this.calculateTeamEfficiency(teamSize, teamExperience, developmentApproach)
    };
  }

  // Detect Industry Pattern for Timeline Standards
  detectIndustryPattern(formData) {
    const projectDescription = (formData.projectDescription || '').toLowerCase();
    const projectName = (formData.projectName || '').toLowerCase();
    const features = (formData.features || []).join(' ').toLowerCase();

    const industryPatterns = {
      ecommerce: ['shop', 'store', 'cart', 'payment', 'product', 'order', 'inventory'],
      healthcare: ['patient', 'medical', 'health', 'clinic', 'hospital', 'appointment'],
      finance: ['bank', 'payment', 'transaction', 'finance', 'loan', 'investment'],
      education: ['student', 'course', 'learning', 'school', 'university', 'education'],
      manufacturing: ['inventory', 'supply', 'production', 'warehouse', 'logistics'],
      government: ['citizen', 'public', 'government', 'municipal', 'civic'],
      startup: ['mvp', 'startup', 'prototype', 'launch', 'scale']
    };

    const allText = `${projectDescription} ${projectName} ${features}`;
    let detectedIndustry = 'general';
    let maxMatches = 0;

    Object.entries(industryPatterns).forEach(([industry, keywords]) => {
      const matches = keywords.filter(keyword => allText.includes(keyword)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        detectedIndustry = industry;
      }
    });

    return {
      industry: detectedIndustry,
      confidence: maxMatches > 0 ? Math.min(maxMatches / 3, 1) : 0.3,
      standardTimeline: this.getIndustryStandardTimeline(detectedIndustry),
      specificFactors: this.getIndustrySpecificFactors(detectedIndustry)
    };
  }

  // Industry Standard Timeline Database
  getIndustryStandardTimeline(industry) {
    const industryStandards = {
      ecommerce: {
        simple: '12-16 weeks',
        medium: '20-28 weeks',
        complex: '32-48 weeks',
        factors: ['Payment integration', 'Inventory management', 'Seasonal considerations']
      },
      healthcare: {
        simple: '16-24 weeks',
        medium: '28-40 weeks',
        complex: '48-72 weeks',
        factors: ['HIPAA compliance', 'Patient data security', 'Medical workflows']
      },
      finance: {
        simple: '20-28 weeks',
        medium: '32-48 weeks',
        complex: '52-78 weeks',
        factors: ['PCI DSS compliance', 'Banking regulations', 'Security audits']
      },
      education: {
        simple: '10-16 weeks',
        medium: '18-28 weeks',
        complex: '32-48 weeks',
        factors: ['Student data privacy', 'Academic calendar', 'Accessibility requirements']
      },
      manufacturing: {
        simple: '16-24 weeks',
        medium: '28-40 weeks',
        complex: '48-72 weeks',
        factors: ['Supply chain integration', 'Quality control', 'Regulatory compliance']
      },
      government: {
        simple: '20-32 weeks',
        medium: '36-52 weeks',
        complex: '60-104 weeks',
        factors: ['Security clearance', 'Accessibility compliance', 'Public procurement']
      },
      startup: {
        simple: '8-12 weeks',
        medium: '14-20 weeks',
        complex: '24-36 weeks',
        factors: ['MVP focus', 'Rapid iteration', 'Scalability planning']
      },
      general: {
        simple: '12-18 weeks',
        medium: '20-32 weeks',
        complex: '36-52 weeks',
        factors: ['Standard development practices', 'Quality assurance', 'Deployment']
      }
    };

    return industryStandards[industry] || industryStandards.general;
  }

  // Industry-Specific Timeline Factors
  getIndustrySpecificFactors(industry) {
    const specificFactors = {
      ecommerce: {
        seasonality: 'Holiday season preparation (+2-4 weeks)',
        compliance: 'PCI DSS certification (+1-2 weeks)',
        integrations: 'Payment gateways, shipping APIs (+2-3 weeks)'
      },
      healthcare: {
        compliance: 'HIPAA compliance review (+3-4 weeks)',
        security: 'Security audits and penetration testing (+2-3 weeks)',
        validation: 'Medical workflow validation (+2-4 weeks)'
      },
      finance: {
        compliance: 'Financial regulations compliance (+4-6 weeks)',
        security: 'Banking-grade security implementation (+3-5 weeks)',
        auditing: 'Financial auditing and reporting (+2-3 weeks)'
      },
      education: {
        accessibility: 'WCAG 2.1 compliance (+1-2 weeks)',
        privacy: 'FERPA compliance (+1-2 weeks)',
        integration: 'LMS and academic system integration (+2-3 weeks)'
      },
      manufacturing: {
        integration: 'ERP and supply chain integration (+3-5 weeks)',
        compliance: 'Industry-specific regulations (+2-4 weeks)',
        quality: 'Quality control system integration (+2-3 weeks)'
      },
      government: {
        security: 'Government security standards (+4-8 weeks)',
        accessibility: 'Section 508 compliance (+2-3 weeks)',
        approval: 'Government approval processes (+4-12 weeks)'
      },
      startup: {
        mvp: 'MVP-focused development (-2-4 weeks)',
        iteration: 'Rapid iteration cycles (ongoing)',
        scaling: 'Scalability architecture (+1-2 weeks)'
      },
      general: {
        testing: 'Comprehensive testing phase (+2-3 weeks)',
        deployment: 'Production deployment (+1-2 weeks)',
        documentation: 'Technical documentation (+1-2 weeks)'
      }
    };

    return specificFactors[industry] || specificFactors.general;
  }

  // Helper Methods for Timeline Analysis
  countProjectModules(formData) {
    let moduleCount = 0;

    // Count from features
    if (formData.features?.length) {
      moduleCount += formData.features.length;
    }

    // Count from user roles (each role typically needs specific modules)
    if (formData.userRoles?.length) {
      moduleCount += Math.ceil(formData.userRoles.length / 2);
    }

    // Minimum modules for any project
    return Math.max(moduleCount, 3);
  }

  normalizeTeamSize(teamSize) {
    if (typeof teamSize === 'number') return teamSize;
    if (typeof teamSize === 'string') {
      const match = teamSize.match(/(\d+)/);
      if (match) return parseInt(match[1]);
    }
    return 3; // Default team size
  }

  normalizeTeamExperience(experience) {
    if (typeof experience === 'string') {
      const exp = experience.toLowerCase();
      if (exp.includes('senior') || exp.includes('expert')) return 'senior';
      if (exp.includes('junior') || exp.includes('beginner')) return 'junior';
      if (exp.includes('mid') || exp.includes('intermediate')) return 'mid';
    }
    return 'mid'; // Default experience level
  }

  calculateTeamEfficiency(teamSize, experience, approach) {
    let efficiency = 1.0;

    // Team size efficiency
    const size = this.normalizeTeamSize(teamSize);
    if (size <= 2) efficiency *= 0.8; // Small team overhead
    else if (size >= 8) efficiency *= 0.9; // Large team coordination overhead

    // Experience efficiency
    const exp = this.normalizeTeamExperience(experience);
    if (exp === 'senior') efficiency *= 1.3;
    else if (exp === 'junior') efficiency *= 0.7;

    // Development approach efficiency
    if (approach?.includes('agile')) efficiency *= 1.1;
    if (approach?.includes('devops')) efficiency *= 1.1;

    return Math.min(efficiency, 1.5); // Cap at 1.5x efficiency
  }

  // Assess Module Complexity from Project Data
  assessModuleComplexity(formData) {
    const modules = [];

    // Extract modules from features
    if (formData.features?.length) {
      formData.features.forEach(feature => {
        modules.push({
          name: feature,
          complexity: this.estimateFeatureComplexity(feature),
          type: 'feature'
        });
      });
    }

    // Add standard modules based on project type
    const standardModules = this.getStandardModules(formData);
    modules.push(...standardModules);

    return {
      totalModules: modules.length,
      complexityBreakdown: modules,
      averageComplexity: this.calculateAverageComplexity(modules)
    };
  }

  // Analyze Technology Stack Complexity
  analyzeTechStackComplexity(formData) {
    const techStack = {
      frontend: formData.frontendTech || [],
      backend: formData.backendTech || [],
      database: formData.databaseTech || [],
      mobile: formData.mobileTech || []
    };

    let complexityScore = 1;

    // Frontend complexity
    if (techStack.frontend.includes('React') || techStack.frontend.includes('Vue')) {
      complexityScore += 0.5;
    }
    if (techStack.frontend.includes('Angular')) {
      complexityScore += 0.7;
    }

    // Backend complexity
    if (techStack.backend.includes('Node.js') || techStack.backend.includes('Python')) {
      complexityScore += 0.5;
    }
    if (techStack.backend.includes('Java') || techStack.backend.includes('.NET')) {
      complexityScore += 0.7;
    }

    // Database complexity
    if (techStack.database.includes('MongoDB') || techStack.database.includes('PostgreSQL')) {
      complexityScore += 0.3;
    }

    // Mobile complexity
    if (techStack.mobile.length > 0) {
      complexityScore += 0.8;
    }

    return {
      score: Math.min(complexityScore, 5),
      level: complexityScore <= 2 ? 'Simple' : complexityScore <= 3.5 ? 'Medium' : 'High',
      technologies: techStack
    };
  }

  // Analyze Integration Complexity
  analyzeIntegrationComplexity(formData) {
    const integrations = formData.integrations || [];
    let complexityScore = 0;

    integrations.forEach(integration => {
      if (integration.includes('Payment') || integration.includes('payment')) {
        complexityScore += 1.5; // Payment integrations are complex
      } else if (integration.includes('Social') || integration.includes('social')) {
        complexityScore += 0.5; // Social integrations are simpler
      } else if (integration.includes('API') || integration.includes('api')) {
        complexityScore += 1.0; // API integrations are medium complexity
      } else {
        complexityScore += 0.7; // Default integration complexity
      }
    });

    return {
      score: complexityScore,
      level: complexityScore === 0 ? 'None' : complexityScore <= 2 ? 'Simple' : complexityScore <= 4 ? 'Medium' : 'High',
      integrations: integrations,
      count: integrations.length
    };
  }

  // Helper Methods for Module Analysis
  estimateFeatureComplexity(feature) {
    const featureName = feature.toLowerCase();

    // High complexity features
    if (featureName.includes('payment') || featureName.includes('analytics') ||
      featureName.includes('reporting') || featureName.includes('integration')) {
      return 'High';
    }

    // Medium complexity features
    if (featureName.includes('user') || featureName.includes('admin') ||
      featureName.includes('search') || featureName.includes('notification')) {
      return 'Medium';
    }

    // Simple features
    return 'Simple';
  }

  getStandardModules(formData) {
    const standardModules = [
      { name: 'User Authentication', complexity: 'Medium', type: 'standard' },
      { name: 'User Management', complexity: 'Medium', type: 'standard' },
      { name: 'Dashboard', complexity: 'Simple', type: 'standard' }
    ];

    // Add modules based on user roles
    if (formData.userRoles?.length > 2) {
      standardModules.push({ name: 'Role Management', complexity: 'Medium', type: 'standard' });
    }

    // Add modules based on platforms
    if (formData.platforms?.includes('Mobile')) {
      standardModules.push({ name: 'Mobile API', complexity: 'High', type: 'standard' });
    }

    return standardModules;
  }

  calculateAverageComplexity(modules) {
    if (modules.length === 0) return 'Simple';

    const complexityScores = modules.map(module => {
      switch (module.complexity) {
        case 'High': return 3;
        case 'Medium': return 2;
        case 'Simple': return 1;
        default: return 2;
      }
    });

    const average = complexityScores.reduce((sum, score) => sum + score, 0) / complexityScores.length;

    if (average <= 1.5) return 'Simple';
    if (average <= 2.5) return 'Medium';
    return 'High';
  }

  // Update context with new section data
  updateSRSContext(contextManager, sectionData, tokens, cost) {
    contextManager.generatedSections.push({
      content: sectionData,
      tokens,
      cost,
      timestamp: new Date().toISOString()
    });

    // Build cumulative context for next sections
    contextManager.sectionContext = contextManager.generatedSections
      .map(s => s.content.substring(0, 500) + '...')
      .join('\n\n');

    contextManager.totalTokensUsed += tokens;
    contextManager.estimatedCost += cost;

    return contextManager;
  }

  // Step 3A: Generate Foundation Sections (Introduction, Overview, User Roles)
  async step3A_GenerateFoundationSections(projectId, contextManager) {
    // Add foundation sections generation request with ALL existing quality controls
    conversationManager.addMessage(projectId, 'user',
      `Generate the FOUNDATION SECTIONS of a comprehensive, professional Software Requirements Specification (SRS) document using all information from our conversation. This must be production-ready, enterprise-grade content following IEEE 830 standards.

**CRITICAL MARKDOWN FORMATTING REQUIREMENT:**
Format the response in clean, professional MARKDOWN format. Use proper Markdown syntax for headings, tables, lists, code blocks, and emphasis. This will be converted to HTML/PDF/Word on the frontend.

**GENERATE THESE FOUNDATION SECTIONS WITH COMPLETE DETAILS:**

1. **Introduction**
   - 1.1 Purpose: Comprehensive project purpose, stakeholders, business objectives, target audience, document scope
   - 1.2 Scope: Detailed system boundaries, what's included/excluded, success criteria, limitations
   - 1.3 Definitions, Acronyms, and Abbreviations: All technical terms, business terminology, industry acronyms
   - 1.4 References: IEEE 830, industry standards, frameworks, documentation, compliance requirements
   - 1.5 Overview: Document navigation, section descriptions, how to use this SRS, reading guide

2. **Overall Description**
   - 2.1 Product Perspective: System context, ecosystem integration, external interfaces, market position
   - 2.2 Product Functions: Core capabilities, feature overview, value proposition, business benefits
   - 2.3 User Classes and Characteristics: Detailed personas, technical proficiency, usage patterns
   - 2.4 Operating Environment: Hardware, software, network, browser requirements, infrastructure
   - 2.5 Design and Implementation Constraints: Technical limitations, regulatory requirements, standards
   - 2.6 User Documentation: Help systems, training materials, support documentation, user guides
   - 2.7 Assumptions and Dependencies: Critical assumptions, external dependencies, risk factors

3. **User Roles and Permissions**
   - **DETAILED PERMISSION MATRIX**:
     * Generate clear table format: "| Feature | Role 1 | Role 2 | Role 3 |"
     * Use visual indicators: "✅ (Full Access), ⚠️ (Limited Access), ❌ (No Access)"
     * Avoid overlapping or ambiguous permissions between roles
     * Include specific features: "Create/Edit/Delete Users, View Analytics, Generate Reports, Manage Templates"
   - **ROLE HIERARCHY WITH CLEAR BOUNDARIES**:
     * Define exact scope of each role without overlap
     * Specify inheritance rules (if any) between roles
     * Include role-specific workflows and user journeys
   - **ACCESS CONTROL SPECIFICATIONS**:
     * Detailed authentication requirements (password policies, MFA, session management)
     * Authorization rules with specific resource access patterns
     * Data access restrictions based on role and organizational hierarchy
   - **USER MANAGEMENT PROCEDURES**:
     * Role assignment and modification workflows
     * User onboarding and offboarding procedures
     * Audit trail requirements for role changes and access modifications

**🎯 ENHANCED QUALITY STANDARDS:**

**MEASURABLE REQUIREMENTS (NO VAGUE STATEMENTS):**
- Replace generic terms like "fast", "scalable", "secure" with specific metrics
- Example: "API response time <500ms for 95% of requests" NOT "fast response"
- Example: "Handle 10,000 concurrent users" NOT "system shall be scalable"
- Example: "99.9% uptime with <2 second recovery" NOT "highly available"

**AI-SPECIFIC REQUIREMENTS (CRITICAL FOR AI FEATURES):**
- Add accuracy thresholds: "95% accuracy for structured fields, 85% for unstructured"
- Add confidence scoring: "Fields with <80% confidence flagged for manual review"
- Add fallback mechanisms: "Failed AI processing triggers manual workflow"
- Add retraining loops: "User corrections feed back into AI model improvement"

**COMPREHENSIVE ERROR HANDLING:**
- Specific error logging requirements with timestamps, user IDs, error types
- Automatic admin notifications for recurring errors (>3 occurrences/hour)
- Quarantine folders for failed document processing
- User-friendly error messages with clear next steps

**DETAILED COMPLIANCE SPECIFICATIONS:**
- Specific data retention policies: "Quotations retained 7 years, logs 2 years"
- Right to erasure implementation: "Personal data anonymized within 72 hours"
- Encrypted exports with watermarking for audit trails
- Specific regulatory compliance (GDPR Article references, HIPAA sections)

**STRUCTURED PERMISSION MATRIX:**
- Generate clear role-permission tables with ✅ ❌ indicators
- Avoid overlapping or ambiguous role definitions
- Include specific feature access for each user role

**REALISTIC TIMELINE ESTIMATION:**
- Include comprehensive testing phases (8+ weeks for complex systems)
- Add AI model validation time (2+ weeks)
- Include integration testing and rollback procedures
- Add 25-30% buffer time with clear justification

**FORMATTING REQUIREMENTS:**
- Professional technical writing style
- Numbered sections with cross-references
- Detailed tables and specifications
- Clear, unambiguous language
- Specific metrics and measurable criteria
- Consistent formatting and structure
- Use of diagrams where applicable

🔁 CRITICAL REMINDERS:
1. **GENERATE MEASURABLE REQUIREMENTS**: Replace all vague terms with specific metrics
   - "Fast" → "Response time <500ms for 95% of requests"
   - "Scalable" → "Handle 10,000 concurrent users with auto-scaling"
   - "Secure" → "AES-256 encryption, TLS 1.3, JWT with 24-hour expiry"

2. **INCLUDE VISUAL AIDS AND STRUCTURED DATA**:
   - Generate permission matrices with ✅ ❌ indicators
   - Create structured risk matrices with probability/impact/mitigation
   - Include API payload examples with JSON request/response
   - Add data flow descriptions for complex processes

3. **AI-SPECIFIC REQUIREMENTS** (if project includes AI):
   - Accuracy thresholds: "95% for structured fields, 85% for unstructured"
   - Confidence scoring and fallback mechanisms
   - Error handling and data preservation procedures
   - Retraining and continuous improvement processes

4. **COMPREHENSIVE ERROR HANDLING**:
   - Specific logging requirements with timestamps and user context
   - Automatic notification thresholds (e.g., >3 errors/hour)
   - User-friendly error messages with clear next steps
   - Data preservation and recovery procedures

5. **REALISTIC TIMELINE CONSIDERATIONS**:
   - Include comprehensive testing phases (8+ weeks for complex systems)
   - Add AI validation time (2+ weeks) if applicable
   - Include deployment and rollback procedures (3+ weeks)
   - Add appropriate buffer time (25-30%) with justification

6. **DETAILED COMPLIANCE SPECIFICATIONS**:
   - Specific regulatory references (GDPR articles, HIPAA sections)
   - Exact data retention periods and deletion procedures
   - Audit trail requirements and reporting mechanisms-

Generate the FOUNDATION SECTIONS (1-3) with complete HTML formatting and full details. This should be comprehensive enough to serve as a professional consulting deliverable.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Use intelligent rate limiting
    const estimatedTokens = conversationManager.estimateTokens(conversation);
    await this.checkRateLimit(estimatedTokens, 'gpt-4-turbo');

    const response = await this.callGPTWithConversationRateLimited(conversation, 'srs_generation');

    const tokens = this.estimateTokens(response);
    const cost = this.updateTokenUsage(tokens, 'gpt-4-turbo');
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    // Update context for next sections
    this.updateSRSContext(contextManager, response, tokens, cost);

    return response;
  }

  // Step 3B: Generate Functional Requirements Sections
  async step3B_GenerateFunctionalSections(projectId, contextManager) {
    // Add functional sections generation with context from previous sections
    conversationManager.addMessage(projectId, 'user',
      `Continue generating the FUNCTIONAL REQUIREMENTS SECTIONS of the comprehensive SRS document. Use the context from previously generated foundation sections to maintain consistency.

**CONTEXT FROM PREVIOUS SECTIONS:**
${contextManager.sectionContext}

**CRITICAL MARKDOWN FORMATTING REQUIREMENT:**
Generate clean, professional MARKDOWN format. Continue with the same professional formatting established in previous sections. Use proper Markdown syntax for headings, tables, lists, and emphasis.

**GENERATE THESE FUNCTIONAL SECTIONS WITH COMPLETE DETAILS:**

4. **System Features (Module-wise Functional Requirements)**
   - **MEASURABLE FUNCTIONAL REQUIREMENTS**: Each requirement must include specific metrics, thresholds, and acceptance criteria
   - **FR-IDs with Detailed Specifications**: FR-001, FR-002, etc. with precise technical details
   - **AI-Specific Requirements** (if applicable):
     * Accuracy thresholds: "AI shall extract data with 95% accuracy for structured fields, 85% for unstructured fields"
     * Confidence scoring: "Fields with confidence score <80% shall be flagged for manual review"
     * Fallback mechanisms: "Failed AI processing shall trigger manual workflow within 30 seconds"
     * Error handling: "AI errors shall be logged with error type, confidence score, and original data preserved"
     * Retraining capability: "User corrections shall be fed back to AI model for continuous improvement"
   - **COMPREHENSIVE ERROR HANDLING**:
     * Specific error logging: "Log timestamp, user ID, error type, and context for all failures"
     * Admin notifications: "Notify admins automatically when error rate exceeds 3 occurrences/hour"
     * User-friendly messages: "Provide clear error messages with specific next steps for users"
     * Data preservation: "Preserve original data in quarantine folder for failed processing"
   - **DETAILED INPUT/OUTPUT SPECIFICATIONS**:
     * Exact data formats, validation rules, field constraints
     * File size limits, supported formats, encoding requirements
     * Response time requirements for each operation
   - **COMPLETE USER STORIES** with measurable acceptance criteria
   - **BUSINESS RULES** with specific validation logic and edge case handling
   - **INTEGRATION POINTS** with detailed API specifications and data exchange formats
   - **PERFORMANCE REQUIREMENTS** with specific metrics (response times, throughput, concurrent users)

5. **External Interface Requirements**
   - 5.1 User Interfaces: Detailed UI/UX specifications, wireframe descriptions, responsive design requirements
   - 5.2 Hardware Interfaces: Server specifications, client requirements, network infrastructure needs
   - 5.3 Software Interfaces: API specifications, database connections, third-party integrations, data formats
   - 5.4 Communications Interfaces: Network protocols, security requirements, data transmission standards

6. **Data Requirements**
   - Complete database schema with tables, fields, relationships, constraints, indexes
   - Data validation rules, business logic, data flow diagrams
   - Data migration requirements, backup strategies, data retention policies
   - Data security and privacy requirements, encryption standards
   - Data integration and synchronization requirements

**QUALITY STANDARDS (STRICTLY MAINTAIN):**
- Each functional requirement must be specific, measurable, testable, achievable, relevant
- Include detailed acceptance criteria and traceability matrix elements
- Add comprehensive error conditions and handling procedures
- Specify integration points and API specifications
- Include performance benchmarks and scalability metrics
- Maintain consistency with foundation sections terminology

🔁 CRITICAL REMINDERS:
1. Reference and build upon the foundation sections already generated
2. Maintain consistent terminology and cross-references
3. Generate COMPLETE, DETAILED content for ALL functional sections
4. Include specific technical details and implementation guidance
5. Add comprehensive tables, diagrams, and structured specifications
6. Ensure seamless flow from previous sections

Generate the FUNCTIONAL REQUIREMENTS SECTIONS (4-6) with complete HTML formatting and full technical details.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Use intelligent rate limiting with delay between calls
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay
    const estimatedTokens = conversationManager.estimateTokens(conversation);
    await this.checkRateLimit(estimatedTokens, 'gpt-4-turbo');

    const response = await this.callGPTWithConversationRateLimited(conversation, 'srs_generation');

    const tokens = this.estimateTokens(response);
    const cost = this.updateTokenUsage(tokens, 'gpt-4-turbo');
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    // Update context for next sections
    this.updateSRSContext(contextManager, response, tokens, cost);

    return response;
  }

  // Step 3C: Generate Technical Specifications Sections
  async step3C_GenerateTechnicalSections(projectId, contextManager) {
    // Add technical sections generation with full context
    conversationManager.addMessage(projectId, 'user',
      `Continue generating the TECHNICAL SPECIFICATIONS SECTIONS of the comprehensive SRS document. Build upon all previously generated sections to maintain consistency and completeness.

**CONTEXT FROM PREVIOUS SECTIONS:**
${contextManager.sectionContext}

**CRITICAL MARKDOWN FORMATTING REQUIREMENT:**
Generate clean, professional MARKDOWN format. Continue with the same professional formatting established in previous sections. Use proper Markdown syntax for headings, tables, lists, and emphasis.

**GENERATE THESE TECHNICAL SECTIONS WITH COMPLETE DETAILS:**

7. **Technical Stack and Architecture**
   - **7.1 System Architecture** (Enhanced with Visual Descriptions):
     * High-level architecture with component interaction flows
     * Data flow diagrams: "User Request → Load Balancer → API Gateway → Microservices → Database"
     * Scalability architecture: "Horizontal scaling with auto-scaling groups, load balancing strategies"
     * Deployment architecture: "Multi-environment setup (Dev/Staging/Production) with CI/CD pipelines"
   - **7.2 Technology Stack** (Detailed Justifications):
     * Frontend: "Specific framework versions with performance benchmarks and compatibility requirements"
     * Backend: "Technology choices with scalability metrics and team expertise considerations"
     * Database: "Selection criteria based on data volume, query patterns, and consistency requirements"
     * Infrastructure: "Cloud provider selection with cost analysis and vendor lock-in considerations"
   - **7.3 Database Design** (Complete Specifications):
     * Schema with exact field types, constraints, and indexes
     * Relationship diagrams with cardinality and referential integrity rules
     * Performance optimization strategies: "Query optimization, indexing strategy, partitioning plans"
     * Data migration and backup strategies with specific procedures
   - **7.4 API Specifications** (With Examples):
     * RESTful endpoints with complete request/response examples
     * Authentication examples: "Bearer token usage, refresh token flow, error handling"
     * Sample payloads: "JSON request/response examples for all major endpoints"
     * Error codes: "Comprehensive HTTP status codes with specific error messages"
     * Rate limiting: "API throttling rules, quota management, and fair usage policies"
   - **7.5 Security Architecture** (Specific Standards):
     * Authentication: "JWT implementation with specific expiry times and refresh mechanisms"
     * Encryption: "AES-256 for data at rest, TLS 1.3 for data in transit with certificate management"
     * Compliance: "Specific regulatory requirements (GDPR Article 32, HIPAA 164.312) with implementation details"
   - **7.6 Integration Architecture** (Detailed Specifications):
     * Third-party service integration patterns with fallback mechanisms
     * API versioning strategy and backward compatibility requirements
     * Data synchronization protocols with conflict resolution strategies

8. **Non-Functional Requirements**
   - **8.1 Performance Requirements (SPECIFIC METRICS REQUIRED)**:
     * Response Times: "API responses <500ms for 95% of requests, <2s for 99% of requests"
     * Throughput: "Handle minimum 1,000 concurrent users, scale to 10,000 users"
     * Database Performance: "Query response time <100ms for 90% of database operations"
     * File Processing: "Document upload and processing complete within 30 seconds for files <10MB"
   - **8.2 Security Requirements (DETAILED SPECIFICATIONS)**:
     * Authentication: "JWT tokens expire after 24 hours, refresh tokens after 30 days"
     * Encryption: "AES-256 for data at rest, TLS 1.3 for data in transit"
     * Password Policy: "Minimum 12 characters, uppercase, lowercase, numbers, special characters"
     * Session Management: "Auto-logout after 30 minutes of inactivity"
     * Compliance: "GDPR Article 32 compliance, HIPAA 164.312 if applicable, SOX if financial data"
   - **8.3 Reliability Requirements (MEASURABLE TARGETS)**:
     * Uptime: "99.9% availability (8.76 hours downtime/year maximum)"
     * Error Rates: "System error rate <0.1%, user-facing errors <0.01%"
     * Recovery: "Automatic failover within 30 seconds, full recovery within 4 hours (RTO)"
     * Data Loss: "Recovery Point Objective (RPO) of 1 hour maximum"
   - **8.4 Usability Requirements (SPECIFIC STANDARDS)**:
     * Accessibility: "WCAG 2.1 AA compliance, screen reader compatible"
     * Mobile Responsiveness: "Support devices from 320px to 2560px width"
     * Load Times: "Page load time <3 seconds on 3G connection"
     * User Training: "New users productive within 30 minutes of training"
   - **8.5 Scalability Requirements (GROWTH PROJECTIONS)**:
     * User Growth: "Support 10x user growth (current to 10x) without architecture changes"
     * Data Growth: "Handle 100TB data storage with <5% performance degradation"
     * Geographic Scaling: "Multi-region deployment capability with <100ms inter-region latency"
   - **8.6 Maintainability Requirements (SPECIFIC STANDARDS)**:
     * Code Coverage: "Minimum 80% unit test coverage, 70% integration test coverage"
     * Documentation: "API documentation auto-generated, user docs updated within 48 hours of changes"
     * Deployment: "Zero-downtime deployments, rollback capability within 5 minutes"

9. **Quality Assurance and Testing**
   - Testing strategy and methodologies (unit, integration, system, acceptance testing)
   - Detailed test cases and scenarios for each functional requirement
   - Performance testing requirements and benchmarks
   - Security testing protocols and penetration testing requirements
   - User acceptance testing criteria and procedures
   - Automated testing and CI/CD integration requirements

**TECHNICAL DEPTH REQUIREMENTS:**
- Include specific metrics, measurements, and SLA definitions
- Provide detailed API endpoint specifications with examples
- Add complete database schema with field types, constraints, and relationships
- Include security protocols, encryption standards, and compliance requirements
- Specify performance benchmarks, monitoring, and alerting requirements
- Add comprehensive logging, debugging, and troubleshooting procedures

🔁 CRITICAL REMINDERS:
1. Build upon and reference all previously generated sections
2. Maintain technical consistency and terminology throughout
3. Generate COMPLETE, DETAILED content for ALL technical sections
4. Include production-ready specifications for development teams
5. Add comprehensive technical diagrams and architecture descriptions
6. Ensure all technical requirements are measurable and testable

Generate the TECHNICAL SPECIFICATIONS SECTIONS (7-9) with complete HTML formatting and full technical depth.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Use intelligent rate limiting with delay
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay
    const estimatedTokens = conversationManager.estimateTokens(conversation);
    await this.checkRateLimit(estimatedTokens, 'gpt-4-turbo');

    const response = await this.callGPTWithConversationRateLimited(conversation, 'srs_generation');

    const tokens = this.estimateTokens(response);
    const cost = this.updateTokenUsage(tokens, 'gpt-4-turbo');
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    // Update context for next sections
    this.updateSRSContext(contextManager, response, tokens, cost);

    return response;
  }

  // Step 3D: Generate Project Management Sections with Timeline Intelligence
  async step3D_GenerateManagementSections(projectId, contextManager) {
    // Add management sections generation with full context and timeline intelligence
    const timelineAnalysis = contextManager.timelineAnalysis || {
      complexity: { score: 5, level: 'Medium', factors: { modules: 5, userRoles: 2, platforms: 1, integrations: 2 } },
      teamData: { size: 3, experience: 'mid', efficiency: 1.0, approach: [] },
      industryPattern: { industry: 'general', confidence: 0.5, standardTimeline: { medium: '20-32 weeks' }, factors: ['Standard development'] }
    };

    conversationManager.addMessage(projectId, 'user',
      `Continue generating the PROJECT MANAGEMENT SECTIONS of the comprehensive SRS document. Complete the document with all remaining sections, building upon all previously generated content.

**CONTEXT FROM ALL PREVIOUS SECTIONS:**
${contextManager.sectionContext}

**🧠 INTELLIGENT TIMELINE ANALYSIS DATA:**
- **Project Complexity**: ${timelineAnalysis.complexity.level} (Score: ${timelineAnalysis.complexity.score}/10)
  - Modules: ${timelineAnalysis.complexity.factors.modules}
  - User Roles: ${timelineAnalysis.complexity.factors.userRoles}
  - Platforms: ${timelineAnalysis.complexity.factors.platforms}
  - Integrations: ${timelineAnalysis.complexity.factors.integrations}

- **Team Analysis**:
  - Size: ${timelineAnalysis.teamData.size} developers
  - Experience: ${timelineAnalysis.teamData.experience} level
  - Efficiency Factor: ${timelineAnalysis.teamData.efficiency}x
  - Development Approach: ${timelineAnalysis.teamData.approach.join(', ') || 'Standard'}

- **Industry Pattern**: ${timelineAnalysis.industryPattern.industry} (${Math.round(timelineAnalysis.industryPattern.confidence * 100)}% confidence)
  - Standard Timeline: ${timelineAnalysis.industryPattern.standardTimeline[timelineAnalysis.complexity.level.toLowerCase()]}
  - Industry Factors: ${timelineAnalysis.industryPattern.factors?.join(', ') || 'Standard development practices'}

**📊 USE THIS ANALYSIS FOR ACCURATE TIMELINE GENERATION**

**CRITICAL MARKDOWN FORMATTING REQUIREMENT:**
Generate clean, professional MARKDOWN format. Continue with the same professional formatting established throughout the document. Use proper Markdown syntax for headings, tables, lists, and emphasis.

**GENERATE THESE FINAL SECTIONS WITH COMPLETE DETAILS:**

10. **Team Structure and Roles**
    - Detailed team composition with specific roles and responsibilities
    - Skills and experience requirements for each role with proficiency levels
    - Team size recommendations based on project scope and timeline
    - Communication and collaboration structure, reporting hierarchy
    - Decision-making process and escalation procedures
    - Resource allocation and workload distribution

11. **Development Methodology and Process**
    - Software Development Life Cycle (SDLC) approach with detailed phases
    - Sprint planning and iteration structure, ceremony definitions
    - Code review and quality assurance processes, standards enforcement
    - Version control and branching strategy, release management
    - Continuous integration and deployment pipeline specifications
    - Change management and configuration control procedures

12. **Timeline Estimation and Project Schedule**
    - **REALISTIC TIMELINE ANALYSIS**:
      * Include comprehensive testing phases: "8+ weeks for complex systems with AI components"
      * AI model validation time: "2+ weeks for AI accuracy testing and bias audits"
      * Integration testing: "3+ weeks for third-party service integration and API testing"
      * Deployment and rollback procedures: "3+ weeks including production deployment and rollback drills"
    - **DETAILED PHASE BREAKDOWN**:
      * Requirements & Design: "4 weeks including stakeholder reviews and approval cycles"
      * Development: "Split by module complexity (Auth: 3w, AI Processing: 6w, Templates: 4w)"
      * Testing: "8 weeks total (Unit: 2w, Integration: 3w, AI Validation: 2w, UAT: 1w)"
      * Deployment: "3 weeks including staging, production, and rollback testing"
    - **ENHANCED BUFFER TIME CALCULATION**:
      * Technical Risk Buffer: "25% for AI/ML components, 20% for standard modules"
      * Integration Risk Buffer: "Additional 2-3 weeks for each major third-party integration"
      * Team Experience Buffer: "Add 15% if team is new to technology stack"
      * Compliance Buffer: "Add 2-4 weeks for GDPR/HIPAA compliance validation"
    - **WEEK-BY-WEEK MILESTONES** with specific deliverables:
      * Each week includes: "Deliverable description, success criteria, team assignments, dependencies"
      * Risk checkpoints: "Weekly risk assessment and mitigation status updates"
    - **MODULE-WISE EFFORT ESTIMATION** with realistic complexity factors:
      * Simple modules: "2-4 weeks", Medium: "4-8 weeks", Complex: "8-16 weeks"
      * AI/ML modules: "Add 50% extra time for training, validation, and bias testing"
    - **RESOURCE ALLOCATION WITH REALISTIC CAPACITY**:
      * Account for holidays, sick leave, training time: "Assume 75% productive capacity"
      * Include code review time: "Add 20% to development estimates for peer reviews"
      * Include documentation time: "Add 10% for technical documentation and user guides"

13. **Page Structure and Module Diagrams**
    - Detailed page structure for each user role with navigation flows
    - User journey mapping and interaction patterns
    - Module interaction diagrams and system integration flows
    - Database entity relationship diagrams with detailed relationships
    - System architecture diagrams and component interactions
    - API flow diagrams and data exchange patterns

14. **Deployment and Infrastructure**
    - Hosting and infrastructure requirements with specifications
    - Environment setup (development, staging, production) with configurations
    - Deployment strategy and rollback procedures, automation requirements
    - Monitoring and logging infrastructure with alerting systems
    - Backup and disaster recovery plans with RTO/RPO specifications
    - Security infrastructure and compliance requirements

15. **Risk Management and Mitigation**
    - **STRUCTURED RISK MATRIX**:
      * Format: "| Risk | Probability | Impact | Mitigation | Owner | Timeline |"
      * Probability Levels: "Low (<20%), Medium (20-60%), High (>60%)"
      * Impact Levels: "Low (minor delays), Medium (significant impact), High (project failure), Critical (business impact)"
    - **AI-SPECIFIC RISKS** (Critical for AI-enabled systems):
      * "AI model bias in processing | Medium | High | Monthly bias audits using diverse datasets | AI Team | Ongoing"
      * "AI accuracy degradation | Low | High | Continuous monitoring with 95% accuracy threshold | QA Team | Weekly"
      * "Training data quality issues | Medium | Medium | Data validation pipeline with quality metrics | Data Team | Pre-deployment"
    - **TECHNICAL RISKS WITH SPECIFIC MITIGATION**:
      * "Third-party API failures | Medium | High | Implement circuit breakers and fallback mechanisms | Backend Team | Development Phase"
      * "Database performance bottlenecks | Low | Medium | Query optimization and indexing strategy | DBA | Performance Testing"
      * "Security vulnerabilities | Medium | Critical | Regular penetration testing and security audits | Security Team | Monthly"
    - **BUSINESS RISKS WITH CONTINGENCY PLANS**:
      * "Regulatory compliance changes | Low | High | Legal review process and compliance monitoring | Legal Team | Quarterly"
      * "Market requirements evolution | Medium | Medium | Agile development with flexible architecture | Product Team | Sprint Reviews"
    - **RESOURCE RISKS WITH BACKUP STRATEGIES**:
      * "Key team member unavailability | Medium | Medium | Cross-training and knowledge documentation | HR/PM | Ongoing"
      * "Skill gap in AI/ML technologies | High | Medium | Training programs and external consultants | Training Team | Pre-project"
    - **TIMELINE RISKS WITH ACCELERATION PLANS**:
      * "Integration delays with third-party services | Medium | High | Parallel development and early integration testing | Integration Team | Development"
      * "Testing phase overruns | High | Medium | Automated testing implementation and additional QA resources | QA Team | Testing Phase"

16. **Appendices and Supporting Documentation**
    - **A. PERMISSION MATRIX** (Visual Role-Feature Mapping):
      * Generate table format: "| Feature | Super Admin | Company Admin | Agent |"
      * Use visual indicators: "✅ (Full Access), ⚠️ (Limited Access), ❌ (No Access)"
      * Include specific features: "Create Users, Delete Data, View Analytics, Generate Reports"
    - **B. API SPECIFICATIONS WITH EXAMPLES**:
      * Sample Request/Response payloads for all endpoints
      * Authentication examples with Bearer tokens
      * Error response examples with HTTP status codes
      * Rate limiting and pagination specifications
    - **C. DATA FLOW DIAGRAMS**:
      * Visual representation of data movement between modules
      * AI processing flow: "PDF Upload → AI Processing → JSON Output → Validation → Storage"
      * User authentication flow with security checkpoints
      * Integration flow with third-party services
    - **D. DATABASE SCHEMA DETAILS**:
      * Complete table structures with field types, constraints, indexes
      * Entity Relationship Diagrams (ERD) with relationships
      * Sample data examples for each table
      * Migration scripts and setup procedures
    - **E. COMPLIANCE DOCUMENTATION**:
      * GDPR compliance checklist with specific article references
      * Data retention policies with exact timeframes
      * Privacy impact assessment results
      * Security audit requirements and schedules
    - **F. TESTING SPECIFICATIONS**:
      * Comprehensive test case examples for each module
      * Performance testing scenarios with load specifications
      * Security testing protocols and penetration testing requirements
      * User acceptance testing criteria with measurable outcomes
    - **G. DEPLOYMENT GUIDES**:
      * Step-by-step deployment procedures
      * Environment configuration specifications
      * Rollback procedures with specific timelines
      * Monitoring and alerting setup instructions
    - **H. REQUIREMENTS TRACEABILITY MATRIX**:
      * Link each requirement to design, development, and testing phases
      * Include verification methods for each requirement
      * Track requirement changes and impact analysis

**🚀 INTELLIGENT TIMELINE GENERATION REQUIREMENTS:**

**COMPREHENSIVE PROJECT ANALYSIS:**
1. **Analyze ALL Project Data**: Use every detail from the 8-step form to assess complexity, scope, and requirements
2. **Team Data Integration**: Extract and analyze team structure, size, experience, and approach from Step 8 form data
3. **Industry Pattern Application**: Apply industry-specific timeline standards and compliance requirements
4. **Technology Stack Impact**: Factor in technology complexity and team familiarity with chosen stack
5. **Integration Complexity**: Assess third-party integrations and their timeline impact

**WEEK-BY-WEEK TIMELINE GENERATION:**
- **Weekly Milestones**: Generate specific deliverables and success criteria for each week
- **Module Progress Tracking**: Show how each module progresses within the weekly timeline
- **Team Assignment**: Specify which team members work on what during each week
- **Dependencies Management**: Clear identification of task dependencies and critical path
- **Quality Gates**: Weekly review points and acceptance criteria

**SMART BUFFER TIME CALCULATION:**
- **Risk Analysis**: Identify project-specific risks that may impact timeline
- **Buffer Determination**: AI-calculated buffer time based on complexity and risk factors
- **Default Buffer**: Apply 20-30% buffer if AI cannot determine specific risks
- **Industry Factors**: Add time for compliance, audits, and industry-specific requirements

**ACCURACY AND REALISM:**
- **Realistic Estimates**: Provide accurate timelines even if longer than expected
- **Industry Standards**: Use proven industry patterns for similar projects
- **Team Efficiency**: Factor in actual team capabilities and experience levels
- **Seasonal Considerations**: Include industry-specific seasonal factors (e.g., e-commerce holiday prep)

**DETAILED OUTPUT REQUIREMENTS:**
- Generate comprehensive timeline with project complexity analysis
- Include team efficiency assessment and resource allocation
- Provide week-by-week schedule with module-wise progress
- Add risk factors, buffer time explanations, and mitigation strategies
- Include industry-specific compliance and regulatory timeline factors

🔁 CRITICAL REMINDERS:
1. Complete the entire SRS document with all remaining sections
2. Maintain consistency with all previously generated sections
3. Generate COMPLETE, DETAILED content for ALL management sections
4. Include production-ready project management documentation
5. Add comprehensive timelines, cost estimates, and resource planning
6. Ensure all sections work together as a cohesive, professional document

Generate the PROJECT MANAGEMENT SECTIONS (10-16) with complete HTML formatting and full project details. This completes the comprehensive SRS document.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Use intelligent rate limiting with delay
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay
    const estimatedTokens = conversationManager.estimateTokens(conversation);
    await this.checkRateLimit(estimatedTokens, 'gpt-4-turbo');

    const response = await this.callGPTWithConversationRateLimited(conversation, 'srs_generation');

    const tokens = this.estimateTokens(response);
    const cost = this.updateTokenUsage(tokens, 'gpt-4-turbo');
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    // Update context for final assembly
    this.updateSRSContext(contextManager, response, tokens, cost);

    return response;
  }

  // Step 3E: Assemble Complete SRS Document with Enhanced Validation
  async step3E_AssembleCompleteSRS(projectId, sections) {
    console.log('🔧 Starting SRS document assembly...');

    // Validate all sections before assembly
    const validatedSections = this.validateSections(sections);

    // Intelligent document assembly with comprehensive cleanup
    const assembledDocument = this.mergeMarkdownSections([
      validatedSections.foundation,
      validatedSections.functional,
      validatedSections.technical,
      validatedSections.management
    ]);

    // Perform final content validation and cleanup
    const cleanedDocument = this.performFinalMarkdownCleanup(assembledDocument);

    // Add final document header and metadata
    const finalSRS = this.wrapCompleteMarkdownDocument(cleanedDocument, projectId);

    // Validate final document structure
    const validationResult = this.validateFinalDocument(finalSRS);
    if (!validationResult.isValid) {
      console.warn('⚠️ Document validation issues:', validationResult.issues);
    }

    // Store final response with comprehensive metadata
    const tokens = this.estimateTokens(finalSRS);
    const cost = this.calculateCost(tokens);
    const assemblyMetadata = {
      sectionsProcessed: Object.keys(sections).length,
      totalCharacters: finalSRS.length,
      validationPassed: validationResult.isValid,
      assemblyTimestamp: new Date().toISOString()
    };

    conversationManager.addMessage(projectId, 'assistant',
      `✅ Complete SRS Document assembled successfully!\n\nAssembly Details:\n- Sections: ${assemblyMetadata.sectionsProcessed}\n- Characters: ${assemblyMetadata.totalCharacters.toLocaleString()}\n- Validation: ${validationResult.isValid ? 'PASSED' : 'ISSUES DETECTED'}\n- Generated: ${assemblyMetadata.assemblyTimestamp}`,
      3, tokens, cost);

    console.log('✅ SRS document assembly completed successfully');
    return finalSRS;
  }

  // Wrap complete Markdown document with professional structure
  wrapCompleteMarkdownDocument(content, projectId) {
    const project = conversationManager.getProject(projectId);
    const projectName = project?.formData?.projectName || 'Software Project';
    const generationDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `# Software Requirements Specification

## ${projectName}

---

**Document Information:**
- **Generated:** ${generationDate}
- **Standard:** IEEE 830 Compliant
- **Version:** 1.0
- **Generated by:** Enterprise SRS Generation System

---

${content}

---

## Document Metadata

| Field | Value |
|-------|-------|
| **Document Generated** | ${generationDate} |
| **Generated by** | Enterprise SRS Generation System |
| **Standard** | IEEE 830 Compliant |
| **Version** | 1.0 |
| **Project** | ${projectName} |

---

*This document was automatically generated using advanced AI technology and industry best practices. It follows IEEE 830 standards and includes comprehensive requirements for professional software development.*`;
  }

  // Validate sections before assembly
  validateSections(sections) {
    const validatedSections = {};

    Object.entries(sections).forEach(([key, content]) => {
      if (!content || typeof content !== 'string') {
        console.warn(`⚠️ Invalid section content for ${key}:`, content);
        validatedSections[key] = `<div class="error-section"><h3>Error: ${key} section could not be generated</h3><p>Please try regenerating this document.</p></div>`;
      } else {
        validatedSections[key] = content;
      }
    });

    return validatedSections;
  }

  // Perform final document cleanup
  performFinalCleanup(document) {
    return document
      // Remove any remaining HTML document tags
      .replace(/<!DOCTYPE[^>]*>/gi, '')
      .replace(/<\/?html[^>]*>/gi, '')
      .replace(/<head[\s\S]*?<\/head>/gi, '')
      .replace(/<\/?body[^>]*>/gi, '')
      .replace(/<style[\s\S]*?<\/style>/gi, '')

      // Clean up multiple consecutive line breaks
      .replace(/\n{3,}/g, '\n\n')

      // Remove empty divs and sections
      .replace(/<div[^>]*>\s*<\/div>/gi, '')
      .replace(/<section[^>]*>\s*<\/section>/gi, '')

      // Fix any broken HTML tags
      .replace(/<([^>]+)(?<!\/)\s*(?<!\/)>/g, (match) => {
        // Ensure proper tag closure
        return match;
      })

      // Clean up extra whitespace
      .trim();
  }

  // Perform final Markdown document cleanup
  performFinalMarkdownCleanup(document) {
    return document
      // Remove any remaining HTML document tags
      .replace(/<!DOCTYPE[^>]*>/gi, '')
      .replace(/<\/?html[^>]*>/gi, '')
      .replace(/<head[\s\S]*?<\/head>/gi, '')
      .replace(/<\/?body[^>]*>/gi, '')
      .replace(/<style[\s\S]*?<\/style>/gi, '')
      .replace(/<script[\s\S]*?<\/script>/gi, '')

      // Remove HTML container elements
      .replace(/<div[^>]*>\s*<\/div>/gi, '')
      .replace(/<section[^>]*>\s*<\/section>/gi, '')
      .replace(/<span[^>]*>\s*<\/span>/gi, '')

      // Clean up multiple consecutive line breaks
      .replace(/\n{4,}/g, '\n\n\n')
      .replace(/\n{3}/g, '\n\n')

      // Fix Markdown formatting issues
      .replace(/#{7,}/g, '######') // Max 6 heading levels
      .replace(/\*{3,}/g, '**') // Fix excessive emphasis

      // Clean up extra whitespace
      .replace(/[ \t]+$/gm, '') // Remove trailing spaces
      .trim();
  }

  // Validate final document structure
  validateFinalDocument(document) {
    const issues = [];

    // Check for multiple DOCTYPE declarations
    const doctypeCount = (document.match(/<!DOCTYPE/gi) || []).length;
    if (doctypeCount > 1) {
      issues.push(`Multiple DOCTYPE declarations found: ${doctypeCount}`);
    }

    // Check for multiple HTML tags
    const htmlOpenCount = (document.match(/<html[^>]*>/gi) || []).length;
    const htmlCloseCount = (document.match(/<\/html>/gi) || []).length;
    if (htmlOpenCount > 1 || htmlCloseCount > 1) {
      issues.push(`Multiple HTML tags found: ${htmlOpenCount} open, ${htmlCloseCount} close`);
    }

    // Check for multiple HEAD sections
    const headCount = (document.match(/<head[\s\S]*?<\/head>/gi) || []).length;
    if (headCount > 1) {
      issues.push(`Multiple HEAD sections found: ${headCount}`);
    }

    // Check for multiple BODY tags
    const bodyOpenCount = (document.match(/<body[^>]*>/gi) || []).length;
    const bodyCloseCount = (document.match(/<\/body>/gi) || []).length;
    if (bodyOpenCount > 1 || bodyCloseCount > 1) {
      issues.push(`Multiple BODY tags found: ${bodyOpenCount} open, ${bodyCloseCount} close`);
    }

    // Check for basic content presence
    if (!document.includes('<h1>') && !document.includes('<h2>')) {
      issues.push('No main headings found in document');
    }

    return {
      isValid: issues.length === 0,
      issues: issues
    };
  }

  // Enhanced Markdown section merging with comprehensive cleanup
  mergeMarkdownSections(sections) {
    return sections
      .map((section, index) => {
        if (!section || typeof section !== 'string') {
          console.warn(`⚠️ Invalid section at index ${index}:`, section);
          return '';
        }

        // Clean up any HTML remnants and format as clean Markdown
        let cleanedSection = section
          // Remove any HTML document structure that might have leaked through
          .replace(/<!DOCTYPE[^>]*>/gi, '')
          .replace(/<\/?html[^>]*>/gi, '')
          .replace(/<head[\s\S]*?<\/head>/gi, '')
          .replace(/<\/?body[^>]*>/gi, '')
          .replace(/<style[\s\S]*?<\/style>/gi, '')
          .replace(/<script[\s\S]*?<\/script>/gi, '')

          // Remove HTML container divs
          .replace(/^<div[^>]*class="container"[^>]*>/, '')
          .replace(/<\/div>\s*$/, '')
          .replace(/^<div[^>]*>\s*$/, '')
          .replace(/^\s*<\/div>$/, '')

          // Clean up extra whitespace and normalize line breaks
          .replace(/\n{3,}/g, '\n\n')
          .trim();

        // Add section separator for better readability
        if (cleanedSection && index > 0) {
          cleanedSection = `\n---\n\n${cleanedSection}`;
        }

        return cleanedSection;
      })
      .filter(section => section.length > 0) // Remove empty sections
      .join('\n\n');
  }

  // Enhanced document wrapper with comprehensive CSS and structure
  wrapCompleteDocument(content, projectId) {
    const project = conversationManager.getProject(projectId);
    const projectName = project?.formData?.projectName || 'Software Project';
    const generationDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Software Requirements Specification - ${projectName}</title>
    <style>
        /* Enhanced Professional CSS Styling */
        * { box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        /* Typography */
        h1 {
            color: #2c3e50;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            font-size: 2.8em;
            text-align: center;
            margin-bottom: 10px;
            font-weight: 700;
        }

        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.4em;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }

        h4 {
            color: #34495e;
            margin-top: 25px;
            margin-bottom: 12px;
            font-size: 1.2em;
        }

        h5 {
            color: #2c3e50;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        h6 {
            color: #34495e;
            margin-top: 15px;
            margin-bottom: 8px;
            font-size: 1em;
        }

        p {
            margin-bottom: 15px;
            text-align: justify;
            color: #555;
        }

        /* Tables */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        th, td {
            border: 1px solid #e0e0e0;
            padding: 15px;
            text-align: left;
        }

        th {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e3f2fd;
            transition: background-color 0.3s ease;
        }

        /* Lists */
        ul, ol {
            padding-left: 30px;
            margin-bottom: 20px;
        }

        li {
            margin: 8px 0;
            line-height: 1.6;
        }

        /* Special Elements */
        .requirement {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-left: 5px solid #3498db;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .priority-high { border-left-color: #e74c3c; }
        .priority-medium { border-left-color: #f39c12; }
        .priority-low { border-left-color: #27ae60; }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .tech-item {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .tech-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .timeline-item {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .risk-item {
            background: linear-gradient(135deg, #fff5f5, #ffebee);
            border: 1px solid #ffcdd2;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.1);
        }

        .team-member {
            background: linear-gradient(135deg, #f0f8ff, #e3f2fd);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid #bbdefb;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }

        .user-story {
            background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
            padding: 15px;
            border-radius: 8px;
            margin: 12px 0;
            border-left: 4px solid #4caf50;
        }

        .acceptance-criteria {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            padding: 15px;
            border-radius: 8px;
            margin: 12px 0;
            border-left: 4px solid #ff9800;
        }

        /* Code Elements */
        .highlight {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
        }

        .code {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            border: 1px solid #e9ecef;
            font-size: 0.9em;
        }

        .api-endpoint {
            background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', Monaco, monospace;
            margin: 15px 0;
            border: 1px solid #c8e6c9;
            overflow-x: auto;
        }

        /* Section Styling */
        .srs-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #eee;
        }

        .srs-section:last-child {
            border-bottom: none;
        }

        /* Document Header */
        .document-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .document-header h1 {
            border: none;
            color: white;
            margin-bottom: 10px;
        }

        .document-header h2 {
            border: none;
            color: #f0f0f0;
            margin: 0;
            font-weight: 300;
        }

        /* Document Footer */
        .document-footer {
            margin-top: 50px;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .document-footer p {
            margin: 5px 0;
            color: #6c757d;
        }

        /* Print Styles */
        @media print {
            body { background: white; padding: 0; }
            .container { box-shadow: none; padding: 20px; }
            .document-header { background: #f8f9fa !important; color: #333 !important; }
            .document-header h1, .document-header h2 { color: #333 !important; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container { padding: 20px; margin: 10px; }
            h1 { font-size: 2.2em; }
            h2 { font-size: 1.6em; }
            .tech-stack { grid-template-columns: 1fr; }
            table { font-size: 0.9em; }
            th, td { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="document-header">
            <h1>Software Requirements Specification</h1>
            <h2>${projectName}</h2>
            <p style="margin-top: 15px; font-size: 1.1em; opacity: 0.9;">
                IEEE 830 Compliant | Generated ${generationDate}
            </p>
        </div>

        ${content}

        <div class="document-footer">
            <p><strong>Document Generated:</strong> ${generationDate}</p>
            <p><strong>Generated by:</strong> Enterprise SRS Generation System</p>
            <p><strong>Standard:</strong> IEEE 830 Compliant</p>
            <p><strong>Version:</strong> 1.0</p>
        </div>
    </div>
</body>
</html>`;
  }

  // Step 3A: Generate Comprehensive SRS Document (Optimized Single Call)
  async step3A_GenerateComprehensiveSRS(projectId) {
    // Add comprehensive SRS generation request
    conversationManager.addMessage(projectId, 'user',
      `Generate a COMPLETE, COMPREHENSIVE Software Requirements Specification (SRS) document using all information from our conversation. This must be a production-ready, enterprise-grade document following IEEE 830 standards.

**REQUIRED COMPLETE STRUCTURE:**

1. **Introduction**
   - 1.1 Purpose: Detailed project purpose, target audience, document scope
   - 1.2 Scope: Specific system capabilities and limitations
   - 1.3 Definitions, Acronyms, and Abbreviations
   - 1.4 References: Industry standards and frameworks
   - 1.5 Overview: Document structure guide

2. **Overall Description**
   - 2.1 Product Perspective: System context and ecosystem fit
   - 2.2 Product Functions: High-level capabilities
   - 2.3 User Classes and Characteristics: Detailed user personas
   - 2.4 Operating Environment: Technical environment requirements
   - 2.5 Design and Implementation Constraints
   - 2.6 User Documentation: Help and training requirements
   - 2.7 Assumptions and Dependencies

3. **User Roles and Permissions**
   - Detailed role definitions with specific permissions
   - User workflows and access control

4. **System Features (Module-wise Functional Requirements)**
   - For EACH module: Description, Priority, Functional Requirements (FR-001, etc.)
   - Input/Output specifications, User Stories, Business Rules
   - Error handling and integration points

5. **External Interface Requirements**
   - 5.1 User Interfaces: UI/UX specifications
   - 5.2 Hardware Interfaces: Hardware requirements
   - 5.3 Software Interfaces: APIs, databases, integrations
   - 5.4 Communications Interfaces: Network protocols

6. **Data Requirements**
   - Database schema design and relationships
   - Data validation rules and migration requirements

7. **Technical Stack and Architecture**
   - 7.1 System Architecture: High-level design
   - 7.2 Technology Stack: Detailed technology choices
   - 7.3 Database Design: Schema, indexes, constraints
   - 7.4 API Specifications: RESTful endpoints
   - 7.5 Security Architecture: Authentication, authorization
   - 7.6 Integration Architecture: Third-party services

8. **Non-Functional Requirements**
   - 8.1 Performance: Response times, throughput, scalability
   - 8.2 Security: Authentication, encryption, compliance
   - 8.3 Reliability: Uptime, error rates, recovery
   - 8.4 Usability: User experience standards
   - 8.5 Scalability: Growth projections
   - 8.6 Maintainability: Code standards, documentation

9. **Quality Assurance and Testing**
   - Testing strategy, test cases, performance testing
   - Security testing and user acceptance criteria

10. **Team Structure and Roles**
    - Detailed team composition with responsibilities
    - Skills requirements and communication structure

11. **Development Methodology and Process**
    - SDLC approach, sprint planning, code review
    - Version control and CI/CD pipeline

12. **Timeline Estimation and Project Schedule**
    - Module-wise development effort (days/weeks)
    - Technology-wise contribution breakdown
    - Detailed project timeline with milestones
    - Critical path analysis and risk factors

13. **Page Structure and Module Diagrams**
    - Detailed page structure for each user role
    - Navigation flow and user journey mapping
    - Module interaction diagrams

14. **Deployment and Infrastructure**
    - Hosting requirements, environment setup
    - Deployment strategy, monitoring, backup plans

15. **Risk Management and Mitigation**
    - Technical, business, resource, and timeline risks
    - Mitigation strategies and contingency plans

16. **Appendices and Supporting Documentation**
    - Glossary, requirements traceability matrix
    - Change log, references, approval section

**QUALITY STANDARDS:**
- IEEE 830 compliance with professional formatting
- Specific technical details for all components
- Clear acceptance criteria for each feature
- Detailed user stories and use cases
- System architecture and data models
- API specifications and security protocols
- Comprehensive testing requirements
- Production-ready documentation

**FORMATTING:**
- Professional technical writing style
- Numbered sections with cross-references
- Detailed tables and specifications
- Clear, unambiguous language
- Specific metrics and measurable criteria
- Consistent formatting and structure
- Use of diagrams where applicable

🔁 Reminders:
1. Do NOT generate partial SRS without all inputs.
2. If user skips some data, persistently ask for clarification.
3. If document becomes long, split into multiple parts but don’t skip or summarize.
4. Use all conversation data to ensure comprehensive coverage.
5. If rate limited, use optimized single-call approach.


**CRITICAL HTML FORMATTING REQUIREMENT:**
Format the ENTIRE response in clean, professional HTML with proper styling for web display. Use this structure:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Software Requirements Specification - [PROJECT_NAME]</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; font-size: 2.5em; text-align: center; }
        h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 8px; margin-top: 30px; }
        h3 { color: #2c3e50; margin-top: 25px; }
        h4 { color: #34495e; margin-top: 20px; }
        .section { margin-bottom: 30px; }
        .requirement { background: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 10px 0; }
        .priority-high { border-left-color: #e74c3c; }
        .priority-medium { border-left-color: #f39c12; }
        .priority-low { border-left-color: #27ae60; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #3498db; color: white; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        .user-story { background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .acceptance-criteria { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .tech-stack { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .tech-item { background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; }
        .timeline-item { background: linear-gradient(90deg, #3498db, #2980b9); color: white; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .risk-item { background: #fff5f5; border: 1px solid #fed7d7; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .team-member { background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0; border: 1px solid #b3d9ff; }
        ul, ol { padding-left: 25px; }
        li { margin: 5px 0; }
        .highlight { background: #fff3cd; padding: 2px 6px; border-radius: 3px; }
        .code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; }
        .api-endpoint { background: #e8f5e8; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
        .toc { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .toc ul { list-style-type: none; }
        .toc a { text-decoration: none; color: #3498db; }
        .toc a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
\`\`\`

Generate a COMPLETE, COMPREHENSIVE HTML-formatted SRS document that matches the quality of professional consulting firms. Include ALL sections with full details, proper HTML structure, and professional styling.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Use intelligent rate limiting
    const estimatedTokens = conversationManager.estimateTokens(conversation);
    await this.checkRateLimit(estimatedTokens, 'gpt-4-turbo');

    const response = await this.callGPTWithConversationRateLimited(conversation, 'srs_generation');

    const tokens = this.estimateTokens(response);
    const cost = this.updateTokenUsage(tokens, 'gpt-4-turbo');
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    return response;
  }

  // Fallback: Single optimized call for rate-limited scenarios
  async step3_FallbackSingleCallSRS(projectId) {
    this.updateProgress?.(75, 'Using optimized single-call generation...');

    // FIXED: Wait longer for rate limit reset
    await new Promise(resolve => setTimeout(resolve, 8000)); // FIXED: Increased from 5s to 8s

    // Use smaller, optimized Markdown-formatted prompt
    conversationManager.addMessage(projectId, 'user',
      `Generate a comprehensive SRS document using all our conversation data. Include: Introduction, User Roles, Functional Requirements (module-wise), Technical Stack, Timeline, Team Structure, and Project Details. Follow IEEE 830 standards.

**CRITICAL: Format the entire response in clean MARKDOWN format with professional structure. Use proper Markdown syntax for headings, tables, lists, and emphasis for professional presentation.**`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);
    const response = await this.callGPTWithConversationRateLimited(conversation, 'srs_generation');

    const tokens = this.estimateTokens(response);
    const cost = this.updateTokenUsage(tokens, 'gpt-4');
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    return response;
  }

  // Step 3A: Generate Core SRS Structure (Introduction, Overview, User Roles)
  async step3A_GenerateCoreStructure(projectId) {
    conversationManager.addMessage(projectId, 'user',
      `Generate the CORE STRUCTURE sections of a professional SRS document. Focus on creating detailed, comprehensive content for these sections:

**GENERATE THESE SECTIONS:**

1. **Introduction**
   - 1.1 Purpose: Detailed project purpose, target audience, and document scope
   - 1.2 Scope: What the system WILL do and what it WON'T do (be specific)
   - 1.3 Definitions, Acronyms, and Abbreviations: All technical terms used
   - 1.4 References: Industry standards, frameworks, documentation
   - 1.5 Overview: Document structure and how to use this SRS

2. **Overall Description**
   - 2.1 Product Perspective: System context, how it fits in larger ecosystem
   - 2.2 Product Functions: High-level capabilities and main features
   - 2.3 User Classes and Characteristics: Detailed user personas and roles
   - 2.4 Operating Environment: Hardware, software, network requirements
   - 2.5 Design and Implementation Constraints: Technical limitations and requirements
   - 2.6 User Documentation: Help systems, manuals, training materials
   - 2.7 Assumptions and Dependencies: Critical assumptions and external dependencies

3. **User Roles and Permissions**
   - Detailed role definitions with specific permissions
   - User workflows and interaction patterns
   - Access control and security considerations

**QUALITY REQUIREMENTS:**
- Write in professional, technical language
- Include specific details, not generic statements
- Add measurable criteria where applicable
- Use industry-standard terminology
- Include business context and rationale
- Make it comprehensive enough for stakeholder review

Generate detailed, production-ready content for these sections using all project information from our conversation.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);
    const response = await this.callGPTWithConversation(conversation);

    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    return response;
  }

  // Step 3B: Generate Detailed Functional Requirements
  async step3B_GenerateFunctionalRequirements(projectId) {
    conversationManager.addMessage(projectId, 'user',
      `Generate DETAILED FUNCTIONAL REQUIREMENTS sections. Create comprehensive, module-wise functional specifications:

**GENERATE THESE SECTIONS:**

4. **System Features (Module-wise Functional Requirements)**
   For each module identified in our conversation, provide:
   - 4.X Module Name (e.g., User Management, Document Management, etc.)
   - Detailed Description: What the module does and why it's needed
   - Priority: High/Medium/Low with business justification
   - Functional Requirements: Specific, testable requirements (FR-001, FR-002, etc.)
   - Input/Output Specifications: Data inputs, outputs, formats
   - User Stories: Detailed user stories with acceptance criteria
   - Business Rules: Validation rules, constraints, logic
   - Error Handling: How errors are managed and displayed
   - Integration Points: How this module connects with others

5. **External Interface Requirements**
   - 5.1 User Interfaces: Detailed UI/UX specifications, wireframe descriptions
   - 5.2 Hardware Interfaces: Hardware requirements and constraints
   - 5.3 Software Interfaces: APIs, databases, third-party integrations
   - 5.4 Communications Interfaces: Network protocols, data formats

6. **Data Requirements**
   - Database schema design
   - Data models and relationships
   - Data validation rules
   - Data migration requirements
   - Backup and recovery specifications

**QUALITY REQUIREMENTS:**
- Each functional requirement must be:
  * Specific and unambiguous
  * Measurable and testable
  * Achievable and realistic
  * Relevant to business needs
  * Time-bound where applicable
- Include detailed acceptance criteria
- Add traceability matrix elements
- Specify error conditions and handling

Generate comprehensive functional requirements that development teams can implement directly.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);
    const response = await this.callGPTWithConversation(conversation);

    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    return response;
  }

  // Step 3C: Generate Technical Specifications
  async step3C_GenerateTechnicalSpecifications(projectId) {
    conversationManager.addMessage(projectId, 'user',
      `Generate TECHNICAL SPECIFICATIONS and NON-FUNCTIONAL REQUIREMENTS. Create detailed technical documentation:

**GENERATE THESE SECTIONS:**

7. **Technical Stack and Architecture**
   - 7.1 System Architecture: High-level architecture diagram description
   - 7.2 Technology Stack: Detailed technology choices with justifications
   - 7.3 Database Design: Schema, relationships, indexes, constraints
   - 7.4 API Specifications: RESTful endpoints, request/response formats
   - 7.5 Security Architecture: Authentication, authorization, data protection
   - 7.6 Integration Architecture: Third-party services, external APIs

8. **Non-Functional Requirements**
   - 8.1 Performance Requirements: Response times, throughput, scalability metrics
   - 8.2 Security Requirements: Authentication, authorization, data encryption
   - 8.3 Reliability Requirements: Uptime, error rates, recovery procedures
   - 8.4 Usability Requirements: User experience standards, accessibility
   - 8.5 Scalability Requirements: Growth projections, load handling
   - 8.6 Maintainability Requirements: Code standards, documentation, updates

9. **System Constraints and Limitations**
   - Technical constraints and limitations
   - Business constraints and requirements
   - Regulatory and compliance requirements
   - Performance limitations and bottlenecks

10. **Quality Assurance and Testing**
    - Testing strategy and methodologies
    - Test cases and scenarios
    - Performance testing requirements
    - Security testing requirements
    - User acceptance testing criteria

**TECHNICAL DEPTH REQUIREMENTS:**
- Include specific metrics and measurements
- Provide detailed API endpoint specifications
- Add database schema with field types and constraints
- Include security protocols and standards
- Specify performance benchmarks and SLAs
- Add monitoring and logging requirements

Generate production-ready technical specifications that architects and developers can use for implementation.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);
    const response = await this.callGPTWithConversation(conversation);

    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    return response;
  }

  // Step 3D: Generate Project Details and Timeline
  async step3D_GenerateProjectDetails(projectId) {
    conversationManager.addMessage(projectId, 'user',
      `Generate PROJECT MANAGEMENT and IMPLEMENTATION DETAILS. Create comprehensive project planning documentation:

**GENERATE THESE SECTIONS:**

11. **Team Structure and Roles**
    - Detailed team composition with roles and responsibilities
    - Skills and experience requirements for each role
    - Team size recommendations based on project scope
    - Communication and collaboration structure
    - Reporting hierarchy and decision-making process

12. **Development Methodology and Process**
    - Software Development Life Cycle (SDLC) approach
    - Sprint planning and iteration structure
    - Code review and quality assurance processes
    - Version control and branching strategy
    - Continuous integration and deployment pipeline

13. **Timeline Estimation and Project Schedule**
    - Module-wise development effort estimation (in days/weeks)
    - Technology-wise contribution breakdown
    - Detailed project timeline with milestones
    - Critical path analysis and dependencies
    - Risk factors and buffer time allocation
    - Resource allocation and scheduling

14. **Page Structure and Module Diagrams**
    - Detailed page structure for each user role
    - Navigation flow and user journey mapping
    - Module interaction diagrams
    - Database entity relationship diagrams
    - System integration flow charts

15. **Deployment and Infrastructure**
    - Hosting and infrastructure requirements
    - Environment setup (development, staging, production)
    - Deployment strategy and rollback procedures
    - Monitoring and logging infrastructure
    - Backup and disaster recovery plans

16. **Risk Management and Mitigation**
    - Technical risks and mitigation strategies
    - Business risks and contingency plans
    - Resource risks and backup plans
    - Timeline risks and acceleration strategies

**PROJECT PLANNING REQUIREMENTS:**
- Provide realistic timeline estimates based on team size
- Include detailed work breakdown structure
- Add resource allocation and cost estimates
- Specify deliverables and acceptance criteria for each phase
- Include quality gates and review checkpoints
- Add change management and scope control procedures

Generate comprehensive project management documentation that can be used for actual project execution.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);
    const response = await this.callGPTWithConversation(conversation);

    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    return response;
  }

  // Step 3E: Merge and Finalize Complete SRS
  async step3E_MergeAndFinalize(projectId, sections) {
    conversationManager.addMessage(projectId, 'user',
      `MERGE AND FINALIZE the complete SRS document. Take all the generated sections and create a cohesive, professional document:

**SECTIONS TO MERGE:**
1. Core Structure: ${sections.coreStructure}
2. Functional Requirements: ${sections.functionalRequirements}
3. Technical Specifications: ${sections.technicalSpecs}
4. Project Details: ${sections.projectDetails}

**FINALIZATION REQUIREMENTS:**

17. **Appendices and Supporting Documentation**
    - Glossary of terms and definitions
    - Requirements traceability matrix
    - Change log and version history
    - References and bibliography
    - Supporting diagrams and charts

18. **Document Formatting and Structure**
    - Professional document formatting
    - Consistent numbering and cross-references
    - Table of contents with page numbers
    - Executive summary for stakeholders
    - Document approval and sign-off section

**FINAL QUALITY CHECKS:**
- Ensure all sections flow logically and cohesively
- Verify no contradictions between sections
- Check that all requirements are traceable
- Validate technical consistency throughout
- Ensure professional language and formatting
- Add cross-references between related sections
- Include comprehensive index and glossary

**OUTPUT FORMAT:**
Generate a complete, professional SRS document that:
- Follows IEEE 830 standards
- Is ready for stakeholder review and approval
- Can be used directly for development planning
- Includes all necessary technical and business details
- Has consistent formatting and professional presentation

Merge all sections into one comprehensive, production-ready Software Requirements Specification document.`, 3);

    const conversation = conversationManager.getOptimizedConversation(projectId);
    const response = await this.callGPTWithConversation(conversation);

    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, 3, tokens, cost);

    return response;
  }

  // Edit with old conversation (expensive but context-aware)
  async editWithOldConversation(projectId, editRequest) {
    // Increment edit count
    conversationManager.incrementEditCount(projectId);

    // Add edit request to existing conversation
    conversationManager.addMessage(projectId, 'user',
      `Edit request: ${editRequest}`);

    // Get full conversation context
    const conversation = conversationManager.getOptimizedConversation(projectId);

    // Make API call with full context
    const response = await this.callGPTWithConversation(conversation);

    // Store edit response
    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);
    conversationManager.addMessage(projectId, 'assistant', response, null, tokens, cost);

    this.updateProgress?.(100, 'SRS editing completed with conversation context!');
    return response;
  }

  // Edit with new conversation (cheap but no context)
  async editWithNewConversation(projectId, editRequest) {
    const project = conversationManager.getProject(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    // Create simple edit prompt without conversation history
    const messages = [
      {
        role: 'system',
        content: 'You are an SRS editor. Make the requested changes to the SRS document.'
      },
      {
        role: 'user',
        content: `Current SRS Document: ${project.generatedSRS}

Edit Request: ${editRequest}

Please make the requested changes and return the updated SRS document.`
      }
    ];

    // Make API call without conversation history (cheaper)
    const response = await this.callGPTWithMessages(messages);

    // Calculate cost (much lower) but don't store for cost saving
    const tokens = this.estimateTokens(response);
    const cost = this.calculateCost(tokens);

    // Don't store this in conversation (cost saving)
    // Just update the project's generated SRS
    project.generatedSRS = response;
    project.lastModified = new Date().toISOString();
    project.totalCost = (project.totalCost || 0) + cost; // Track total cost

    this.updateProgress?.(100, 'SRS editing completed with fresh context!');
    return response;
  }

  // FIXED: Validate conversation size before API call
  validateConversationSize(conversation) {
    const totalTokens = conversationManager.estimateTokens(conversation);

    if (totalTokens > this.tokenManager.maxConversationTokens) {
      console.warn(`⚠️ Conversation too large: ${totalTokens} tokens (max: ${this.tokenManager.maxConversationTokens})`);
      return false;
    }

    return true;
  }

  // FIXED: Split long prompts automatically
  splitLongPrompt(prompt, maxTokens = 8000) {
    const estimatedTokens = this.estimateTokens(prompt);

    if (estimatedTokens <= maxTokens) {
      return [prompt];
    }

    console.log(`📝 Splitting long prompt: ${estimatedTokens} tokens -> chunks of ${maxTokens} tokens`);

    // Split by sections (looking for markdown headers)
    const sections = prompt.split(/(?=^#{1,6}\s)/m);
    const chunks = [];
    let currentChunk = '';

    for (const section of sections) {
      const sectionTokens = this.estimateTokens(section);
      const currentTokens = this.estimateTokens(currentChunk);

      if (currentTokens + sectionTokens > maxTokens && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = section;
      } else {
        currentChunk += section;
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    console.log(`✅ Split into ${chunks.length} chunks`);
    return chunks;
  }

  // Call GPT with intelligent rate limiting and optimization
  async callGPTWithConversationRateLimited(conversation, contentType = 'general', retries = 2) {
    // FIXED: Validate conversation size first
    if (!this.validateConversationSize(conversation)) {
      throw new Error('Conversation too large - please reduce content or use conversation optimization');
    }

    const model = this.selectOptimalModelForContent(conversation, contentType);
    const estimatedTokens = conversationManager.estimateTokens(conversation);

    // Check rate limits before making request
    await this.checkRateLimit(estimatedTokens, model);

    return await this.callGPTWithConversation(conversation, retries);
  }

  // Call GPT with conversation array
  // OPTIMIZED with timeout and retry logic to prevent hanging
  async callGPTWithConversation(conversation, retries = 2) {
    const model = this.selectOptimalModel(conversation);

    // Enhanced AI training system with comprehensive SRS knowledge
    const enhancedConversation = [
      {
        role: 'system',
        content: `You are an EXPERT SRS GENERATION AI AGENT trained on global industry standards and best practices. You have comprehensive knowledge of:

**🌍 GLOBAL SRS STANDARDS & FRAMEWORKS:**
- IEEE 830-1998: International SRS documentation standard
- ISO/IEC/IEEE 29148: Systems and software engineering requirements
- SWEBOK Guide: Software Engineering Body of Knowledge
- BABOK Guide: Business Analysis Body of Knowledge
- Agile/Scrum: User stories, epics, acceptance criteria
- DevOps: CI/CD pipeline requirements, infrastructure as code
- Cloud-Native: Microservices, containerization, scalability patterns

**🏢 INDUSTRY EXPERTISE (ALL SECTORS):**
- E-commerce: Payment systems, inventory, user management, analytics
- Healthcare: HIPAA compliance, patient data, medical workflows
- Finance: PCI DSS, banking regulations, transaction processing
- Education: LMS systems, student management, assessment tools
- Manufacturing: ERP systems, supply chain, quality control
- Government: Security clearance, compliance, public services
- Startups: MVP requirements, scalability planning, cost optimization

**🧠 INTELLIGENT ANALYSIS CAPABILITIES:**
- Auto-detect business domain from project description
- Fill missing requirements using industry best practices
- Suggest technology stacks based on project needs
- Recommend security standards based on data sensitivity
- Provide scalability recommendations based on user projections
- Include compliance requirements based on industry/region

**📋 EXPERT-LEVEL AUTO-COMPLETION:**
When users skip details or provide minimal information:
1. Analyze project context and business domain
2. Apply industry-specific best practices and standards
3. Generate comprehensive requirements using expert knowledge
4. Include modern development practices (Agile, DevOps, Cloud)
5. Add appropriate security, compliance, and scalability requirements
6. Provide detailed technical specifications and architecture recommendations

**📝 OUTPUT FORMAT:**
- Generate MARKDOWN format with professional structure
- Follow IEEE 830 standards with modern enhancements
- Use specific, measurable requirements (NO vague terms like "fast", "scalable", "secure")
- Include comprehensive visual aids:
  * Permission matrices with ✅ ❌ indicators
  * Structured risk matrices with probability/impact/mitigation
  * API examples with JSON request/response payloads
  * Data flow descriptions for complex processes
- Add detailed compliance specifications with regulatory references
- Include realistic timeline estimates with comprehensive testing phases
- Provide AI-specific requirements with accuracy thresholds and fallback mechanisms
- Generate comprehensive error handling procedures with specific logging requirements

**🎯 QUALITY TARGETS:**
- Replace generic statements with measurable metrics
- Include visual aids and structured data for clarity
- Provide production-ready documentation for immediate development use
- Ensure enterprise-grade quality suitable for Fortune 500 companies

Always provide complete, expert-level SRS documents that exceed industry standards and can be used immediately for professional software development across any industry.`
      },
      ...conversation
    ];

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 90000); // 90 second timeout for comprehensive generation

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.openaiKey}`
          },
          body: JSON.stringify({
            model: model,
            messages: enhancedConversation,
            max_tokens: this.getOptimalTokenLimit(model), // Smart token limit based on model
            temperature: 0.1 // Very low temperature for consistent, professional SRS output
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();

        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
          throw new Error('Invalid response format from OpenAI API');
        }

        return data.choices[0].message.content;

      } catch (error) {
        console.warn(`API call attempt ${attempt + 1} failed:`, error.message);

        if (attempt === retries) {
          throw new Error(`API call failed after ${retries + 1} attempts: ${error.message}`);
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  // Call GPT with simple messages array
  // OPTIMIZED with timeout to prevent hanging
  async callGPTWithMessages(messages) {
    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 45000); // 45 second timeout

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.openaiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: messages,
          max_tokens: 4000,
          temperature: 0.3
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid response format from OpenAI API');
      }

      return data.choices[0].message.content;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  // Get optimal token limit based on model and rate limits
  getOptimalTokenLimit(model) {
    const limits = this.tokenManager.limits[model];
    if (!limits) return 4000;

    // FIXED: Conservative limits to avoid "max_tokens too large" errors
    switch (model) {
      case 'gpt-4-turbo':
        return 3500; // FIXED: Reduced from 6000 to stay within 4096 limit
      case 'gpt-4o':
        return 3000; // FIXED: Reduced from 5000 to stay within 4096 limit
      case 'gpt-4':
        return 2500; // FIXED: Reduced from 4000 for safety margin
      case 'gpt-3.5-turbo':
        return 2000; // FIXED: Reduced from 3000 for safety margin
      default:
        return 2500;
    }
  }

  // Select optimal model based on conversation length
  selectOptimalModel(conversation) {
    const estimatedTokens = conversationManager.estimateTokens(conversation);

    // FIXED: For SRS generation, prefer more capable models with proper token limits
    if (estimatedTokens > 4000) {
      return 'gpt-4-turbo'; // FIXED: Reduced from 6000 to stay within limits
    } else if (estimatedTokens > 2500) {
      return 'gpt-4o'; // FIXED: Reduced from 3000 for safety
    } else {
      return 'gpt-4'; // Standard model for shorter conversations
    }
  }

  // Estimate tokens (rough calculation)
  estimateTokens(text) {
    return Math.ceil(text.length / 4);
  }

  // Calculate cost based on tokens
  calculateCost(tokens) {
    // GPT-4 pricing: $0.03 input + $0.06 output per 1K tokens
    // Simplified calculation assuming 50/50 split
    return (tokens / 1000) * 0.045; // Average cost
  }

  // Generate clarification questions for incomplete project data
  async generateClarificationQuestions(formData) {
    try {
      const prompt = `Based on the following project information, generate 5-7 clarifying questions to better understand the requirements:

Project: ${formData.projectName || 'Untitled Project'}
Description: ${formData.projectDescription || 'No description'}
Goal: ${formData.mainGoal || 'Not specified'}
Platforms: ${formData.selectedPlatforms?.join(', ') || 'Not specified'}

Generate numbered questions (1., 2., etc.) that would help create a comprehensive SRS document.`;

      const systemPrompt = 'You are a requirements analyst expert at identifying missing information for software projects.';

      return await this.generateContent(prompt, systemPrompt);
    } catch (error) {
      console.error('Clarification questions generation failed:', error);
      throw error;
    }
  }

  // Generate project timeline
  async generateTimeline(formData) {
    try {
      const prompt = `Generate a realistic project timeline for the following software project:

Project: ${formData.projectName || 'Untitled Project'}
Description: ${formData.projectDescription || 'No description'}
Platforms: ${formData.selectedPlatforms?.join(', ') || 'Not specified'}
Technologies: ${[...(formData.frontendTech || []), ...(formData.backendTech || [])].join(', ') || 'Not specified'}
Team Size: ${Object.values(formData.teamMembers || {}).reduce((sum, count) => sum + (parseInt(count) || 0), 0) || 3} members

Provide a detailed timeline with phases, milestones, and estimated durations.`;

      const systemPrompt = 'You are a project management expert specializing in software development timelines.';

      return await this.generateContent(prompt, systemPrompt);
    } catch (error) {
      console.error('Timeline generation failed:', error);
      throw error;
    }
  }

  // Generate content using AI provider
  async generateContent(prompt, systemPrompt) {
    try {
      if (this.provider === 'openai' && this.openaiKey) {
        return await this.generateWithOpenAI(prompt, systemPrompt);
      } else if (this.provider === 'gemini' && this.geminiKey) {
        return await this.generateWithGemini(prompt, systemPrompt);
      } else {
        // Try fallback provider
        if (this.openaiKey) {
          return await this.generateWithOpenAI(prompt, systemPrompt);
        } else if (this.geminiKey) {
          return await this.generateWithGemini(prompt, systemPrompt);
        } else {
          throw new Error('No AI provider configured');
        }
      }
    } catch (error) {
      console.error('AI Generation Error:', error);
      throw error;
    }
  }

  // OpenAI API integration
  async generateWithOpenAI(prompt, systemPrompt) {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openaiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        max_tokens: 4000,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  // Gemini API integration
  async generateWithGemini(prompt, systemPrompt) {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${this.geminiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `${systemPrompt}\n\n${prompt}`
          }]
        }]
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  // Check if AI is configured
  isConfigured() {
    return !!(this.openaiKey || this.geminiKey);
  }

  // Get current provider info
  getProviderInfo() {
    return {
      primary: this.provider,
      secondary: this.provider === 'openai' ? 'gemini' : 'openai',
      configured: {
        openai: !!this.openaiKey,
        gemini: !!this.geminiKey
      }
    };
  }
}

export default new AIService();
