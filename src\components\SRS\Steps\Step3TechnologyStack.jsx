import { useState, useRef, useMemo, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  Typography,
  Button,
  Space,
  Alert,
  Divider,
} from "antd";
import { Zap, Plus } from "lucide-react";
import { motion } from "framer-motion";
import { TECHNOLOGY_OPTIONS } from "../../../constants/srsFormData";

const { Title } = Typography;

const Step3TechnologyStack = ({ form, setAllFormData, AllFormData }) => {
  const selectedPlatforms =
    AllFormData?.["2"]?.platforms || AllFormData?.platforms || [];
  const techStack = AllFormData?.["3"]?.technology || AllFormData?.technology;

  const [techOptions, setTechOptions] = useState({
    frontend: [...TECHNOLOGY_OPTIONS.frontend],
    backend: [...TECHNOLOGY_OPTIONS.backend],
    mobile: [...TECHNOLOGY_OPTIONS.mobile],
    database: [...TECHNOLOGY_OPTIONS.database],
    storage: [...TECHNOLOGY_OPTIONS.storage],
  });
  const [newTech, setNewTech] = useState({
    frontend: "",
    backend: "",
    mobile: "",
    database: "",
    storage: "",
  });
  console.log(newTech);
  useEffect(() => {
    const ExistTech = techStack || {
      frontend: techStack?.frontend || "",
      backend: techStack?.backend || "",
      mobile: techStack?.mobile || "",
      database: techStack?.database || "",
      storage: techStack?.storage || "",
    };
    console.log(ExistTech, AllFormData);
    setNewTech(ExistTech);
    // onChangeFormStore("technology", ExistTech);
  }, []);

  const inputRefs = {
    frontend: useRef(null),
    backend: useRef(null),
    mobile: useRef(null),
    database: useRef(null),
    storage: useRef(null),
  };

  // Smart dependency analysis based on platform selection
  const platformAnalysis = useMemo(() => {
    const platforms = selectedPlatforms || [];

    return {
      hasWeb: platforms.some(
        (p) =>
          p.includes("Web Application") ||
          p.includes("Admin Dashboard") ||
          p.includes("Browser Extension")
      ),
      hasMobile: platforms.some(
        (p) => p.includes("Mobile App") || p.toLowerCase().includes("mobile")
      ),
      hasDesktop: platforms.some((p) => p.includes("Desktop Application")),
      hasAPI: platforms.some((p) => p.includes("API Only")),
      hasKiosk: platforms.some((p) => p.includes("Kiosk")),
      platforms,
    };
  }, [selectedPlatforms]);

  // Add custom technology with popupRender
  const addCustomTech = (category, e) => {
    e.preventDefault();
    const value = newTech?.[category]?.trim();
    if (value && !techOptions[category].includes(value)) {
      setTechOptions((prev) => ({
        ...prev,
        [category]: [...prev[category], value],
      }));
      setNewTech((prev) => ({ ...prev, [category]: "" }));
      setTimeout(() => {
        inputRefs[category].current?.focus();
      }, 0);
    }
  };

  // Get technology categories based on platform selection
  const getTechCategories = () => {
    const categories = [];

    // Frontend - always show if web platforms selected
    if (platformAnalysis.hasWeb || platformAnalysis.hasKiosk) {
      categories.push({
        key: "frontend",
        label: "Frontend Technologies",
        required: true,
        description: "User interface technologies",
      });
    }

    // Backend - show for most platforms except pure frontend
    if (
      platformAnalysis.hasWeb ||
      platformAnalysis.hasMobile ||
      platformAnalysis.hasAPI ||
      platformAnalysis.hasDesktop
    ) {
      categories.push({
        key: "backend",
        label: "Backend Technologies",
        required: true,
        description: "Server-side technologies",
      });
    }

    // Mobile - only show if mobile platforms selected
    if (platformAnalysis.hasMobile) {
      categories.push({
        key: "mobile",
        label: "Mobile Technologies",
        required: true,
        description: "Mobile app development technologies",
      });
    }

    // Database - show for most platforms that need data storage
    if (
      platformAnalysis.hasWeb ||
      platformAnalysis.hasMobile ||
      platformAnalysis.hasAPI ||
      platformAnalysis.hasDesktop
    ) {
      categories.push({
        key: "database",
        label: "Database Technologies",
        required: true,
        description: "Data storage and management",
      });
    }

    // Storage - optional for all platforms
    categories.push({
      key: "storage",
      label: "Storage Solutions",
      required: false,
      description: "File storage and CDN services",
    });

    return categories;
  };

  const onChangeFormStore = (key, value) => {
    setAllFormData((prev) => ({
      ...prev,
      ["3"]: {
        ...prev["3"],
        [key]: value,
      },
    }));
  };

  const techCategories = getTechCategories();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <Zap className="mr-3 text-blue-600" size={24} />
          Technology Stack Selection
        </Title>

        {/* Platform Analysis Alert */}
        {selectedPlatforms?.length === 0 ? (
          <Alert
            message="Platform Selection Required"
            description="Please select platforms in the previous step to see relevant technology options."
            type="warning"
            showIcon
            className="mb-6"
          />
        ) : (
          <Alert
            message="Smart Technology Recommendations"
            description={
              <div>
                Based on your platform selection:{" "}
                <strong>{platformAnalysis.platforms.join(", ")}</strong>
                <br />
                <small>
                  {platformAnalysis.hasWeb && "• Web technologies enabled "}
                  {platformAnalysis.hasMobile &&
                    "• Mobile technologies enabled "}
                  {platformAnalysis.hasAPI && "• API technologies enabled "}
                  {platformAnalysis.hasDesktop &&
                    "• Desktop technologies enabled "}
                </small>
              </div>
            }
            type="info"
            showIcon
            className="mb-6"
          />
        )}

        <Row gutter={[24, 24]}>
          {techCategories?.map(({ key, label, required, description }) => (
            <Col xs={24} md={12} key={key}>
              <Form.Item
                // name={`${key}`}
                label={
                  <div>
                    <span>{label}</span>
                    {required && <span className="text-red-500 ml-1">*</span>}
                    <div className="text-xs text-gray-500 font-normal">
                      {description}
                    </div>
                  </div>
                }
                rules={
                  required
                    ? [
                        {
                          required: true,
                          message: `Please select ${label.toLowerCase()}`,
                        },
                      ]
                    : []
                }
              >
                <Select
                  mode="multiple"
                  placeholder={`Select ${label.toLowerCase()}`}
                  size="large"
                  value={techStack?.[key] || []}
                  className="w-full"
                  filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={(value) => {
                    // onChangeFormStore(`${key}`, value);
                    onChangeFormStore("technology", {
                      ...techStack,
                      [key]: value,
                    });
                  }}
                  popupRender={(menu) => (
                    <>
                      {menu}
                      <Divider style={{ margin: "8px 0" }} />
                      <Space style={{ padding: "0 8px 4px" }}>
                        <Input
                          placeholder={`Add custom ${label.toLowerCase()}`}
                          ref={inputRefs[key]}
                          value={newTech?.[key]}
                          onChange={(e) =>
                            setNewTech((prev) => ({
                              ...prev,
                              [key]: e.target.value,
                            }))
                          }
                          onKeyDown={(e) => {
                            e.stopPropagation();
                            if (e.key === "Enter") {
                              addCustomTech(key, e);
                            }
                          }}
                          style={{ width: 180 }}
                        />
                        <Button
                          type="text"
                          icon={<Plus size={14} />}
                          onClick={(e) => addCustomTech(key, e)}
                          // disabled={!newTech?.[key]?.trim()}
                          disabled={
                            typeof newTech?.[key] !== "string" ||
                            !newTech[key].trim()
                          }
                        >
                          Add
                        </Button>
                      </Space>
                    </>
                  )}
                  options={techOptions[key].map((tech) => ({
                    label: tech,
                    value: tech,
                  }))}
                />
              </Form.Item>
            </Col>
          ))}
        </Row>

        {/* Additional Technology Notes */}
        <Row gutter={[24, 24]} className="mt-6">
          <Col xs={24}>
            <Form.Item
              name="customTechNotes"
              label="Additional Technology Requirements"
            >
              <Input.TextArea
                rows={3}
                placeholder="Describe any specific technology requirements, versions, or custom solutions..."
                className="rounded-lg"
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Technology Dependencies Info */}
        <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
          <Title level={5} className="text-green-800 mb-2">
            Technology Selection Guide
          </Title>
          <ul className="text-sm text-green-700 space-y-1">
            <li>
              • Technology options are automatically filtered based on your
              platform selection
            </li>
            <li>
              • Mobile technologies appear only when mobile platforms are
              selected
            </li>
            <li>
              • Frontend technologies are required for web-based platforms
            </li>
            <li>
              • Backend and database are recommended for data-driven
              applications
            </li>
          </ul>
        </div>
      </Card>
    </motion.div>
  );
};

export default Step3TechnologyStack;
