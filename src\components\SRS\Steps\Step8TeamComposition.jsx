import {
  Card,
  Row,
  Col,
  Form,
  InputNumber,
  Select,
  Typography,
  Alert,
  Divider,
} from "antd";
import { Rocket, Users, TrendingUp } from "lucide-react";
import { motion } from "framer-motion";
import { TEAM_ROLES, EXPERIENCE_LEVELS } from "../../../constants/srsFormData";

const { Title } = Typography;

const Step8TeamComposition = ({
  // teamMembers,
  // setTeamMembers,
  form,
  setAllFormData,
  AllFormData,
}) => {
  const teamMembers = AllFormData?.["6"]?.teamMembers || {};
  // Calculate total team members
  const totalMembers = Object.values(teamMembers).reduce(
    (sum, count) => sum + (count || 0),
    0
  );

  // Get team cost estimation (rough calculation)
  const getTeamCostEstimation = () => {
    const roleCosts = {
      "Project Manager": 80000,
      "Business Analyst": 70000,
      "UX/UI Designer": 65000,
      "Frontend Developer": 75000,
      "Backend Developer": 80000,
      "Full Stack Developer": 85000,
      "Mobile Developer": 80000,
      "DevOps Engineer": 90000,
      "QA Engineer": 60000,
      "Database Administrator": 75000,
      "Technical Lead": 100000,
      "Product Owner": 85000,
      "Scrum Master": 75000,
      "Content Writer": 45000,
      "System Administrator": 70000,
    };

    let totalCost = 0;
    Object.entries(teamMembers).forEach(([role, count]) => {
      if (count && roleCosts[role]) {
        totalCost += roleCosts[role] * count;
      }
    });

    return totalCost;
  };

  // Get role categories
  const getRoleCategories = () => {
    return {
      Management: ["Project Manager", "Product Owner", "Scrum Master"],
      "Analysis & Design": ["Business Analyst", "UX/UI Designer"],
      Development: [
        "Frontend Developer",
        "Backend Developer",
        "Full Stack Developer",
        "Mobile Developer",
      ],
      Infrastructure: [
        "DevOps Engineer",
        "Database Administrator",
        "System Administrator",
      ],
      "Quality & Content": ["QA Engineer", "Technical Lead", "Content Writer"],
    };
  };

  const onChangeFormStore = (key, value) => {
    setAllFormData((prev) => ({
      ...prev,
      ["6"]: {
        ...prev["6"],
        [key]: value,
      },
    }));
  };

  const roleCategories = getRoleCategories();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <Rocket className="mr-3 text-blue-600" size={24} />
          Team Composition & Structure
        </Title>

        {/* Team Overview */}
        <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg border border-blue-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Users className="text-blue-600" size={20} />
                <span className="font-semibold text-gray-700">
                  Total Members
                </span>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {totalMembers}
              </div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <TrendingUp className="text-green-600" size={20} />
                <span className="font-semibold text-gray-700">
                  Est. Annual Cost
                </span>
              </div>
              <div className="text-lg font-bold text-green-600">
                ${getTeamCostEstimation().toLocaleString()}
              </div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Rocket className="text-purple-600" size={20} />
                <span className="font-semibold text-gray-700">Team Type</span>
              </div>
              <div className="text-sm font-medium text-purple-600">
                {totalMembers <= 3
                  ? "Small Team"
                  : totalMembers <= 8
                  ? "Medium Team"
                  : totalMembers <= 15
                  ? "Large Team"
                  : "Enterprise Team"}
              </div>
            </div>
          </div>
        </div>

        {/* Team Roles by Category */}
        {Object.entries(roleCategories).map(([category, roles]) => (
          <div key={category} className="mb-6">
            <Title
              level={4}
              className="mb-4 text-gray-700 border-b border-gray-200 pb-2"
            >
              {category}
            </Title>
            <Row gutter={[16, 16]}>
              {roles.map((role) => (
                <Col xs={24} sm={12} md={8} lg={6} key={role}>
                  <div className="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow">
                    <Form.Item label={role} className="mb-2">
                      <InputNumber
                        min={0}
                        max={20}
                        placeholder={`Enter ${role} count`}
                        value={teamMembers[role]}
                        onChange={(value) => {
                          // setTeamMembers((prev) => ({
                          //   ...prev,
                          //   [role]: value || 0,
                          // }));
                          const newTeamMembers = {
                            ...teamMembers,
                            [role]: value,
                          };
                          onChangeFormStore("teamMembers", newTeamMembers);
                          // setAllFormData((prev) => ({
                          //   ...prev,
                          //   teamMembers: {
                          //     ...prev.teamMembers,
                          //     [role]: value,
                          //   },
                          // }));
                        }}
                        className="w-full"
                        size="large"
                      />
                    </Form.Item>
                    {teamMembers[role] > 0 && (
                      <div className="text-xs text-gray-500">
                        {teamMembers[role]} member
                        {teamMembers[role] > 1 ? "s" : ""}
                      </div>
                    )}
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        ))}

        <Divider />

        {/* Team Experience and Structure */}
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Form.Item
              name="teamExperienceLevel"
              label="Overall Team Experience Level"
            >
              <Select
                placeholder="Select team experience level"
                size="large"
                onChange={(value) => {
                  onChangeFormStore("teamExperienceLevel", value);
                  // setAllFormData((prev) => ({
                  //   ...prev,
                  //   teamExperienceLevel: value,
                  // }))
                }}
                options={EXPERIENCE_LEVELS.map((level) => ({
                  label: level,
                  value: level,
                }))}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item name="teamStructure" label="Team Structure">
              <Select
                placeholder="Select team structure"
                size="large"
                onChange={(value) => {
                  onChangeFormStore("teamStructure", value);
                  // setAllFormData((prev) => ({ ...prev, teamStructure: value }))
                }}
                options={[
                  { label: "In-house Team", value: "inhouse" },
                  { label: "Remote Team", value: "remote" },
                  { label: "Hybrid Team", value: "hybrid" },
                  { label: "Outsourced Team", value: "outsourced" },
                  { label: "Mixed (In-house + Contractors)", value: "mixed" },
                ]}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item name="projectDuration" label="Expected Project Duration">
              <Select
                placeholder="Select project duration"
                size="large"
                onChange={(value) => {
                  onChangeFormStore("projectDuration", value);
                  // setAllFormData((prev) => ({
                  //   ...prev,
                  //   projectDuration: value,
                  // }));
                }}
                options={[
                  { label: "1-3 months", value: "1-3" },
                  { label: "3-6 months", value: "3-6" },
                  { label: "6-12 months", value: "6-12" },
                  { label: "1-2 years", value: "1-2" },
                  { label: "2+ years", value: "2+" },
                  { label: "Ongoing/Maintenance", value: "ongoing" },
                ]}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item name="budgetRange" label="Project Budget Range">
              <Select
                placeholder="Select budget range"
                size="large"
                onChange={(value) => {
                  onChangeFormStore("budgetRange", value);
                  // setAllFormData((prev) => ({ ...prev, budgetRange: value }));
                }}
                options={[
                  { label: "Under $50K", value: "under-50k" },
                  { label: "$50K - $100K", value: "50k-100k" },
                  { label: "$100K - $250K", value: "100k-250k" },
                  { label: "$250K - $500K", value: "250k-500k" },
                  { label: "$500K - $1M", value: "500k-1m" },
                  { label: "Over $1M", value: "over-1m" },
                  { label: "Not Decided", value: "undecided" },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Team Recommendations */}
        {totalMembers > 0 && (
          <Alert
            message="Team Composition Analysis"
            description={
              <div className="space-y-2">
                <p>
                  <strong>Team Size:</strong> {totalMembers} members -{" "}
                  {totalMembers <= 3
                    ? "Ideal for small projects with clear scope"
                    : totalMembers <= 8
                    ? "Good balance for medium complexity projects"
                    : totalMembers <= 15
                    ? "Suitable for large, complex projects"
                    : "Enterprise-level team for very large projects"}
                </p>
                <p>
                  <strong>Estimated Annual Cost:</strong> $
                  {getTeamCostEstimation().toLocaleString()}
                  (rough estimate based on average market rates)
                </p>
                {totalMembers > 10 && (
                  <p className="text-orange-600">
                    <strong>Note:</strong> Large teams may require additional
                    coordination and management overhead.
                  </p>
                )}
              </div>
            }
            type="info"
            showIcon
            className="mt-6"
          />
        )}

        {/* Team Guidelines */}
        <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <Title level={5} className="text-yellow-800 mb-2">
            Team Planning Guidelines
          </Title>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Consider project complexity when determining team size</li>
            <li>
              • Balance senior and junior developers for knowledge transfer
            </li>
            <li>
              • Include dedicated QA and DevOps roles for quality and efficiency
            </li>
            <li>
              • Plan for 10-20% additional capacity for unexpected requirements
            </li>
            <li>
              • Consider time zone differences for remote team coordination
            </li>
          </ul>
        </div>
      </Card>
    </motion.div>
  );
};

export default Step8TeamComposition;
