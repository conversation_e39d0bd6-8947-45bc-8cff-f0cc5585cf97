import {
    FileText,
    <PERSON>ting<PERSON>,
    Zap,
    Users,
    Target,
    Shield,
    Clock,
    Rocket,
} from "lucide-react";
import React from "react";

// Define steps with icons
export const srsStepsList = [
    {
        title: "Basic Project Information",
        description: "Project name, description, and main goals",
        icon: React.createElement(FileText, { size: 20 }),
    },
    {
        title: "Platform & Deployment",
        description: "Target platforms and hosting preferences",
        icon: React.createElement(Settings, { size: 20 }),
    },
    {
        title: "Technology Stack",
        description: "Frontend, backend, and database technologies",
        icon: React.createElement(Zap, { size: 20 })
    },
    {
        title: "User Roles Definition",
        description: "Define user roles and their permissions",
        icon: React.createElement(Users, { size: 20 }),
    },
    {
        title: "Functional Modules",
        description: "Key features and module descriptions",
        icon: React.createElement(Target, { size: 20 })
    },
    {
        title: "Third-Party Integrations",
        description: "External services and APIs",
        icon: React.createElement(Shield, { size: 20 })
    },
    {
        title: "Development Lifecycle",
        description: "Methodology and collaboration tools",
        icon: React.createElement(Clock, { size: 20 })
    },
    {
        title: "Team Composition",
        description: "Team roles and experience levels",
        icon: React.createElement(Rocket, { size: 20 })
    },
];

// SRS Steps Configuration
export const srsSteps = [
    { title: "Basic Info", description: "Project fundamentals" },
    { title: "Platform", description: "Deployment & hosting" },
    { title: "Technology", description: "Tech stack selection" },
    { title: "User Roles", description: "System users & permissions" },
    // { title: "Modules", description: "Functional requirements" },
    // { title: "Integrations", description: "Third-party services" },
    { title: "Development", description: "Lifecycle & tools" },
    { title: "Team", description: "Project team structure" },
];

// SOLUTION: Step-specific field mapping to prevent data loss
export const STEP_FIELD_MAPPING = {
    0: ["projectName", "projectDescription", "mainGoal"], // Step 1: Basic Info
    1: ["selectedPlatforms", "deploymentPreference"], // Step 2: Platform & Deployment
    2: [
        "frontendTech",
        "backendTech",
        "databaseTech",
        "mobileTech",
        "hostingPreference",
    ], // Step 3: Technology Stack
    3: ["userRoles"], // Step 4: User Roles (handled separately)
    // 4: ["functionalModules"], // Step 5: Functional Modules (handled separately)
    5: [
        "integrations_payment",
        "integrations_authentication",
        "integrations_maps",
        "integrations_analytics",
        "integrations_email",
        "integrations_storage",
        "integrations_social",
        "integrations_communication",
        "integrations_search",
        "integrations_cms",
        "customIntegrations",
    ], // Step 6: Integrations
    6: [
        "developmentApproach",
        "projectTimeline",
        "budgetRange",
        "tools_projectManagement",
        "tools_versionControl",
        "tools_communication",
        "tools_documentation",
        "tools_design",
        "customTools",
    ], // Step 7: Development Lifecycle
    7: ["teamMembers", "teamSize", "teamExperience"], // Step 8: Team Composition (handled separately)
};