import { <PERSON>, Row, Col, Form, Input, Button, Space, Typography } from "antd";
import {
  Users,
  Plus,
  Minus,
  DeleteIcon,
  Delete,
  Trash,
  SquareX,
} from "lucide-react";
import { motion } from "framer-motion";
import { useCallback } from "react";

const { Title } = Typography;
const { TextArea } = Input;

const Step4UserRoles = ({ form, setAllFormData, AllFormData }) => {
  const userRoles = AllFormData?.["4"]?.userRoles || [
    { name: "", actions: "" },
  ];

  // useEffect(() => {
  //   if (!userRoles) {
  //     onChangeFormStore("userRoles", [{ name: "", actions: "" }]);
  //   }
  // }, []);
  // Memoized helper functions for better performance
  const addUserRole = useCallback(() => {
    // setUserRoles((prev) => [...prev, { name: "", actions: "" }]);
    setAllFormData((prev) => {
      const existingRoles = prev?.["4"]?.userRoles || [];
      const newUserRoles = [...existingRoles, { name: "", actions: "" }];
      return {
        ...prev,
        ["4"]: {
          ...prev["4"],
          userRoles: newUserRoles,
        },
      };
    });
    // const newUserRoles = [...userRoles];
    // newUserRoles.push({ name: "", actions: "" });
    // onChangeFormStore("userRoles", newUserRoles);
    // setAllFormData((prev) => ({
    //   ...prev,
    //   userRoles: [...prev.userRoles, { name: "", actions: "" }],
    // }));
  }, []);

  const removeUserRole = useCallback((index) => {
    setAllFormData((prev) => {
      const existingRoles = prev?.["4"]?.userRoles || [];
      const newUserRoles = existingRoles.filter((_, i) => i !== index);
      return {
        ...prev,
        ["4"]: {
          ...prev["4"],
          userRoles: newUserRoles,
        },
      };
    });
    //   return {
    //     ...prev,
    //     userRoles: prev.userRoles.filter((_, i) => i !== index),
    //   };
    // });
  }, []);

  const updateUserRole = useCallback((index, field, value) => {
    setAllFormData((prev) => {
      const existingRoles = prev?.["4"]?.userRoles || [
        { name: "", actions: "" },
      ];
      const newUserRoles = [...existingRoles];
      newUserRoles[index][field] = value;
      return {
        ...prev,
        ["4"]: {
          ...prev["4"],
          userRoles: newUserRoles,
        },
      };
    });
  }, []);
  // const onChangeFormStore = (key, value) => {;
  //   setAllFormData((prev) => ({
  //     ...prev,
  //     ["4"]: {
  //       ...prev["4"],
  //       [key]: value,
  //     },
  //   }));
  // };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <Users className="mr-3 text-blue-600" size={24} />
          User Roles Definition
        </Title>

        <div className="space-y-6">
          {userRoles?.map((role, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="bg-gray-50 border border-gray-200 relative">
                <div className="flex absolute top-2 right-2">
                  {userRoles.length > 1 && (
                    <Button
                      danger
                      icon={<SquareX size={16} className="m-0" />}
                      onClick={() => removeUserRole(index)}
                      className="w-full pt-[2px]"
                      size="small"
                    />
                    //   Remove
                    // </Button>
                  )}
                </div>
                <Row gutter={[16, 16]} align="middle">
                  <Col xs={24} md={24}>
                    <Form.Item
                      label={`Role ${index + 1} Name`}
                      required
                      className="mb-0"
                    >
                      <Input
                        placeholder="Enter role name (e.g., Admin, User, Manager)"
                        value={role.name}
                        onChange={(e) =>
                          updateUserRole(index, "name", e.target.value)
                        }
                        size="large"
                        className="rounded-lg"
                      />
                    </Form.Item>
                  </Col>

                  {/* <Col xs={24} md={10}>
                    <Form.Item
                      label="Actions/Permissions"
                      required
                      className="mb-0"
                    >
                      <TextArea
                        rows={2}
                        placeholder="Describe what this role can do (e.g., create posts, manage users, view reports)"
                        value={role.actions}
                        onChange={(e) =>
                          updateUserRole(index, "actions", e.target.value)
                        }
                        className="rounded-lg"
                      />
                    </Form.Item>
                  </Col> */}

                  {/* <Col xs={24} md={4}>
                    <div className="flex flex-col space-y-2">
                      {userRoles.length > 1 && (
                        <Button
                          danger
                          icon={<Minus size={16} />}
                          onClick={() => removeUserRole(index)}
                          className="w-full"
                          size="small"
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                  </Col> */}
                </Row>

                {/* Role Description */}
                <Row className="mt-4">
                  <Col xs={24}>
                    <Form.Item
                      label="Role Description (Optional)"
                      className="mb-0"
                    >
                      <TextArea
                        placeholder="Brief description of this role's purpose in the system"
                        value={role.actions || ""}
                        onChange={(e) =>
                          updateUserRole(index, "actions", e.target.value)
                        }
                        className="rounded-lg"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="w-full flex justify-center mt-6">
          <Button
            type="primary"
            icon={<Plus size={16} />}
            onClick={addUserRole}
            className="w-full"
            size="middle"
          >
            Add Role
          </Button>
        </div>

        {/* Role Guidelines */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <Title level={5} className="text-blue-800 mb-2">
            Role Definition Guidelines
          </Title>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>
              • Define clear, specific roles that users will have in your system
            </li>
            <li>
              • Include both functional permissions and business
              responsibilities
            </li>
            <li>
              • Consider different access levels (admin, user, viewer, etc.)
            </li>
            <li>
              • Think about workflow-specific roles (approver, creator,
              reviewer)
            </li>
          </ul>
        </div>

        {/* Quick Add Common Roles */}
        {/* <div className="mt-4">
          <Title level={5} className="mb-3">
            Quick Add Common Roles
          </Title>
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-700 mb-3">
              Click to quickly add common roles, or create your own custom roles
              using the input fields above:
            </p>
            <Space wrap>
              {[
                "Admin",
                "User",
                "Manager",
                "Viewer",
                "Editor",
                "Moderator",
                "Customer",
                "Guest",
              ].map((roleName) => (
                <Button
                  key={roleName}
                  size="small"
                  onClick={() => {
                    const exists = userRoles.some(
                      (role) =>
                        role.name.toLowerCase() === roleName.toLowerCase()
                    );
                    if (!exists) {
                      addUserRole();
                      const newIndex = userRoles.length;
                      setTimeout(() => {
                        updateUserRole(newIndex, "name", roleName);
                      }, 100);
                    }
                  }}
                  className="text-blue-600 border-blue-300 hover:bg-blue-50"
                >
                  + {roleName}
                </Button>
              ))}
            </Space>
          </div>
        </div> */}
      </Card>
    </motion.div>
  );
};

export default Step4UserRoles;
