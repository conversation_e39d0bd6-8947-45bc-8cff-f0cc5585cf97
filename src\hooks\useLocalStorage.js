import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for localStorage management with automatic JSON serialization
 * @param {string} key - localStorage key
 * @param {*} initialValue - Initial value if key doesn't exist
 * @returns {Array} [value, setValue, removeValue, isLoading, error]
 */
export const useLocalStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = useState(initialValue);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get value from localStorage on mount
  useEffect(() => {
    try {
      setIsLoading(true);
      setError(null);

      const item = window.localStorage.getItem(key);
      if (item) {
        const parsedValue = JSON.parse(item);
        setStoredValue(parsedValue);
      } else {
        setStoredValue(initialValue);
      }
    } catch (err) {
      console.error(`Error reading localStorage key "${key}":`, err);
      setError(err.message);
      setStoredValue(initialValue);
    } finally {
      setIsLoading(false);
    }
  }, [key]); // Removed initialValue to prevent infinite loops with array/object references

  // Set value in localStorage
  const setValue = useCallback((value) => {
    try {
      setError(null);

      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      setStoredValue(valueToStore);

      if (valueToStore === undefined) {
        window.localStorage.removeItem(key);
      } else {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (err) {
      console.error(`Error setting localStorage key "${key}":`, err);
      setError(err.message);
    }
  }, [key, storedValue]);

  // Remove value from localStorage
  const removeValue = useCallback(() => {
    try {
      setError(null);
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (err) {
      console.error(`Error removing localStorage key "${key}":`, err);
      setError(err.message);
    }
  }, [key]); // Removed initialValue to prevent infinite loops

  // Clear all localStorage
  const clearAll = useCallback(() => {
    try {
      setError(null);
      window.localStorage.clear();
      setStoredValue(initialValue);
    } catch (err) {
      console.error('Error clearing localStorage:', err);
      setError(err.message);
    }
  }, []); // Removed initialValue to prevent infinite loops

  // Get storage size
  const getStorageSize = useCallback(() => {
    try {
      let total = 0;
      for (let key in localStorage) {
        if (Object.prototype.hasOwnProperty.call(localStorage, key)) {
          total += localStorage[key].length + key.length;
        }
      }
      return total;
    } catch (err) {
      console.error('Error calculating storage size:', err);
      return 0;
    }
  }, []);

  return [storedValue, setValue, removeValue, isLoading, error, { clearAll, getStorageSize }];
};

/**
 * Hook for managing form data in localStorage with auto-save
 * @param {string} key - localStorage key
 * @param {Object} initialData - Initial form data
 * @param {number} autoSaveDelay - Auto-save delay in milliseconds (default: 30000)
 * @returns {Object} Form data management object
 */
export const useFormStorage = (key, initialData = {}, autoSaveDelay = 30000) => {
  const [formData, setFormData, removeFormData, isLoading, error] = useLocalStorage(key, initialData);
  const [lastSaved, setLastSaved] = useState(null);
  const [isDirty, setIsDirty] = useState(false);

  // Auto-save functionality
  useEffect(() => {
    if (!isDirty || isLoading) return;

    const autoSaveTimer = setTimeout(() => {
      setLastSaved(new Date());
      setIsDirty(false);
    }, autoSaveDelay);

    return () => clearTimeout(autoSaveTimer);
  }, [formData, isDirty, isLoading, autoSaveDelay]);

  // Update form data and mark as dirty
  const updateFormData = useCallback((updates) => {
    setFormData(prevData => {
      const newData = typeof updates === 'function' ? updates(prevData) : { ...prevData, ...updates };
      setIsDirty(true);
      return newData;
    });
  }, [setFormData]);

  // Manual save
  const saveNow = useCallback(() => {
    setLastSaved(new Date());
    setIsDirty(false);
    return formData;
  }, [formData]);

  // Reset form data
  const resetFormData = useCallback(() => {
    setFormData(initialData);
    setIsDirty(false);
    setLastSaved(null);
  }, [setFormData, initialData]);

  return {
    formData,
    updateFormData,
    saveNow,
    resetFormData,
    removeFormData,
    isLoading,
    error,
    lastSaved,
    isDirty,
    autoSaveDelay
  };
};

/**
 * Hook for managing SRS history in localStorage (FIXED - No loops)
 * @returns {Object} History management object
 */
export const useSRSHistory = () => {
  const [history, setHistory, removeHistory, isLoading, error] = useLocalStorage('srs_history', []);

  // Prevent infinite loops by memoizing functions with stable dependencies

  // Add new SRS to history (FIXED - No loops)
  const addToHistory = useCallback((srsData) => {
    if (!srsData || !srsData.id) {
      console.error('Invalid SRS data for addToHistory');
      return;
    }

    setHistory(prevHistory => {
      const newHistory = [srsData, ...prevHistory];
      return newHistory.slice(0, 50);
    });
  }, [setHistory]);

  // Remove SRS from history (FIXED - No loops)
  const removeFromHistory = useCallback((srsId) => {
    if (!srsId) {
      console.error('Invalid SRS ID for removeFromHistory');
      return;
    }

    setHistory(prevHistory => prevHistory.filter(item => item.id !== srsId));
  }, [setHistory]);

  // Update SRS in history (FIXED - No loops)
  const updateInHistory = useCallback((srsId, updates) => {
    if (!srsId || !updates) {
      console.error('Invalid parameters for updateInHistory');
      return;
    }

    setHistory(prevHistory =>
      prevHistory.map(item =>
        item.id === srsId ? { ...item, ...updates } : item
      )
    );
  }, [setHistory]);

  // Get SRS by ID (FIXED - Stable reference)
  const getSRSById = useCallback((srsId) => {
    if (!srsId) return null;
    return history.find(item => item?.id === srsId) || null;
  }, [history]);

  // Search history (FIXED - Stable reference)
  const searchHistory = useCallback((query) => {
    if (!query || !query.trim()) return history;

    const lowercaseQuery = query.toLowerCase();
    return history.filter(item =>
      item?.projectInfo?.name?.toLowerCase().includes(lowercaseQuery) ||
      item?.projectInfo?.description?.toLowerCase().includes(lowercaseQuery)
    );
  }, [history]);

  // Save in-progress SRS (fixed to prevent loops)
  const saveInProgress = useCallback((progressData) => {
    try {
      // Validate input data
      if (!progressData || typeof progressData !== 'object') {
        console.error('Invalid progress data provided to saveInProgress');
        return null;
      }

      const inProgressDocument = {
        id: progressData.id || `srs_progress_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        projectInfo: {
          name: progressData.formData?.projectName || 'Untitled Project',
          description: progressData.formData?.projectDescription || '',
          mainGoal: progressData.formData?.mainGoal || ''
        },
        content: '', // No content yet for in-progress
        formData: progressData.formData || {},
        progressData: {
          currentStep: progressData.currentStep || 0,
          userRoles: progressData.userRoles || [],
          functionalModules: progressData.functionalModules || [],
          teamMembers: progressData.teamMembers || {},
          selectedPlatforms: progressData.selectedPlatforms || [],
          conversationHistory: progressData.conversationHistory || []
        },
        metadata: {
          generatedAt: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          status: 'in-progress',
          version: '1.0.0',
          documentType: 'In-Progress SRS',
          currentStep: progressData.currentStep || 0,
          totalSteps: 6,
          completionPercentage: Math.round(((progressData.currentStep + 1 || 1) / 6) * 100),
          wordCount: 0,
          estimatedReadTime: 0
        }
      };

      // Get current history from localStorage directly to avoid state dependency
      const currentHistory = JSON.parse(localStorage.getItem('srs_history') || '[]');

      // Remove any existing in-progress SRS (only one allowed)
      const filteredHistory = currentHistory.filter(item =>
        item?.metadata?.status !== 'in-progress' && item?.id !== inProgressDocument?.id
      );

      const newHistory = [inProgressDocument, ...filteredHistory].slice(0, 50);
      console.log(newHistory, "newHistory", filteredHistory, currentHistory);
      // Update localStorage directly
      localStorage.setItem('srs_history', JSON.stringify(newHistory));

      // Update state without causing loops
      setHistory(newHistory);

      console.log('✅ Progress saved successfully:', inProgressDocument.id);
      return inProgressDocument;
    } catch (error) {
      console.error('❌ Error saving progress:', error);
      return null;
    }
  }, [setHistory]);

  // Get current in-progress SRS (FIXED - Stable reference)
  const getInProgressSRS = useCallback(() => {
    return history.find(doc => doc?.metadata?.status === 'in-progress') || null;
  }, [history]);

  // Check if there's an in-progress SRS (FIXED - Direct check to avoid dependency)
  const hasInProgressSRS = useCallback(() => {
    return history.some(doc => doc?.metadata?.status === 'in-progress');
  }, [history]);

  // Complete in-progress SRS (convert to completed) - optimized
  const completeInProgressSRS = useCallback((srsId, finalContent) => {
    try {
      if (!srsId || !finalContent) {
        console.error('Invalid parameters for completeInProgressSRS');
        return;
      }

      setHistory(prevHistory =>
        prevHistory.map(item => {
          if (item?.id === srsId) {
            const wordCount = finalContent.split(' ').length;
            return {
              ...item,
              content: finalContent,
              metadata: {
                ...item.metadata,
                status: 'completed',
                lastModified: new Date().toISOString(),
                completionPercentage: 100,
                wordCount: wordCount,
                estimatedReadTime: Math.ceil(wordCount / 200)
              }
            };
          }
          return item;
        })
      );
    } catch (error) {
      console.error('Error completing in-progress SRS:', error);
    }
  }, [setHistory]);

  return {
    history,
    addToHistory,
    removeFromHistory,
    updateInHistory,
    getSRSById,
    searchHistory,
    clearHistory: removeHistory,

    // In-progress functionality
    saveInProgress,
    getInProgressSRS,
    hasInProgressSRS,
    completeInProgressSRS,

    isLoading,
    error,
    count: history.length
  };
};
